"""
This module defines FastAPI routers for chat operations.
"""

# Standard imports
import json
from typing import AsyncGenerator, Optional
from uuid import UUID

# External imports
from azure.core.exceptions import ResourceNotFoundError
from fastapi import APIRouter, HTTPException, Query, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, StreamingResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import get_current_span, SpanKind, Status, StatusCode

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v1 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.models.chat.chat_models_v1 import ChatRequest
from src.app.prompts.documents.document_prompts_v1 import (
    DOCUMENT_WEAK_POINTS_PROMPT,
    DOCUMENT_STRONG_POINTS_PROMPT,
    DOCUMENT_TIMELINE_PROMPT,
    DOCUMENT_QUESTIONS_PROMPT,
    DOCUMENT_SUMMARY_PROMPT,
)

from src.app.services.chat.chat_services_v1 import ChatHistoryService, ChatService
from src.app.services.retrieval.retrieval_services_v1 import RetrieverService


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


async def create_chat_service(
    organization_id: UUID, project_id: Optional[UUID], chat_request: ChatRequest
) -> ChatService:
    """
    Creates and initializes a ChatService instance with the necessary components.

    This function initializes the required services to handle a chat request,
    including:
    - A `ChatHistoryService` to retrieve the chat history of the organization.
    - A `RetrieverService` (if a `project_id` or jurisprudence is provided) to retrieve
    project-specific information.

    Args:
        organization_id (UUID): The unique identifier of the organization making the request.
        project_id (Optional[UUID]): The unique identifier of the project, if any.
        If not provided, project retrieval is skipped.
        chat_request (ChatRequest): The chat request object containing user details
        and session information.

    Returns:
        ChatService: An instance of the `ChatService` initialized with the provided parameters.
    """
    chat_history_service = ChatHistoryService(
        organization_id, chat_request.user_id, chat_request.session_id
    )
    chat_history = chat_history_service.get_chat_history()
    retriever_service = RetrieverService(
        organization_id=organization_id,
        include_jurisprudence=chat_request.jurisprudence,
        project_id=project_id if project_id else None,
        legal_areas=chat_request.legal_areas if chat_request.legal_areas else None,
        legal_sub_areas=(
            chat_request.legal_sub_areas if chat_request.legal_sub_areas else None
        ),
    )

    return ChatService(organization_id, project_id, chat_history, retriever_service)


async def stream_generator(
    chat_service: ChatService, chat_request: ChatRequest
) -> AsyncGenerator[str, None]:
    """
    Generates a stream of chat responses.
    Each chunk is formatted as JSON for easy parsing by the C# client.
    Args:
        chat_service (ChatService): The chat service instance to use for streaming.
        chat_request (ChatRequest): The chat request object containing user input.
    Yields:
        StreamingChunk: A chunk of the AI response as it is generated
    Raises:
        HTTPException: If an error occurs during the streaming process.
    """

    try:
        async for chunk in chat_service.astream_chat_response(chat_request):
            # Convert chunk to JSON that C# can easily parse
            chunk_dict = chunk.model_dump()
            yield f"{json.dumps(chunk_dict)}\n"

    except ResourceNotFoundError as e:
        error_response = {
            "error": "ResourceNotFoundError",
            "message": str(e),
            "status_code": status.HTTP_404_NOT_FOUND,
        }
        yield f"{json.dumps(error_response)}\n"
        return

    except Exception as e:
        error_response = {
            "error": "InternalServerError",
            "message": str(e),
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        }
        yield f"{json.dumps(error_response)}\n"
        return


def _get_prompt(predefined_prompt):
    """
    Get the predefined prompt based on the predefined prompt value.

    Args:
        predefined_prompt (int): The predefined prompt value.

    Returns:
        str: The predefined prompt.
    """

    if predefined_prompt == 1:
        return DOCUMENT_TIMELINE_PROMPT
    elif predefined_prompt == 2:
        return DOCUMENT_QUESTIONS_PROMPT
    elif predefined_prompt == 3:
        return DOCUMENT_SUMMARY_PROMPT
    elif predefined_prompt == 4:
        return DOCUMENT_WEAK_POINTS_PROMPT
    elif predefined_prompt == 5:
        return DOCUMENT_STRONG_POINTS_PROMPT
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Invalid predefined prompt value.",
    )


async def handle_chat_request(
    organization_id: UUID,
    project_id: Optional[UUID],
    chat_request: ChatRequest,
    use_streaming: bool,
) -> StreamingResponse | JSONResponse:
    """
    Handles the core logic for chat operations.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (Optional[UUID]): The ID of the project, if applicable.
        chat_request (ChatRequest): The chat request containing the input message.
        use_streaming (bool): Whether to use streaming response.

    Returns:
        JSONResponse: The chat response.

    Raises:
        HTTPException: If an error occurs during the chat operation.
    """

    try:
        if chat_request.predefined_prompt:
            chat_request.user_message = _get_prompt(chat_request.predefined_prompt)
        chat_service = await create_chat_service(
            organization_id, project_id, chat_request
        )
        if use_streaming:
            return StreamingResponse(
                stream_generator(chat_service, chat_request),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream",
                    "X-Accel-Buffering": "no",
                },
            )

        chat_response = await chat_service.get_chat_response(chat_request)
        return JSONResponse(
            content=jsonable_encoder(chat_response),
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        api_logger.error(
            "Error handling chat request for organization %s and project %s.",
            organization_id,
            project_id or "(no project id)",
            exc_info=True,
        )

        current_span = get_current_span()
        if current_span:
            current_span.set_status(Status(StatusCode.ERROR, str(e)))

        raise e


@api_router.post(
    "/{organization_id}/chat",
    description="Handles chat completions.",
    operation_id="create_chat_completion",
    response_description="The chat completion.",
    response_model=None,  # Disable response model since we're returning different types
    summary="Chat completion. ☑️",
)
async def create_chat_completion(
    request: Request,
    organization_id: UUID,
    chat_request: ChatRequest,
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
) -> StreamingResponse | JSONResponse:
    """
    Handles chat operations for a organization.

    Args:
        organization_id (UUID): The ID of the organization.
        chat_request (ChatRequest): The chat request containing the input message.
        use_streaming (bool): Whether to use streaming response.

    Returns:
        JSONResponse: The chat response.
        StreamingResponse: The streaming chat response.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await handle_chat_request(
                organization_id,
                project_id=None,
                chat_request=chat_request,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, chat_request.session_id, "chat response"
        )


@api_router.post(
    "/{organization_id}/projects/{project_id}/chat",
    description="Handles chat completions for a project.",
    operation_id="create_chat_completion_for_project",
    response_description="The chat completion.",
    response_model=None,  # Disable response model since we're returning different types
    summary="Chat completion for a project. ☑️",
)
async def create_chat_completion_for_project(
    request: Request,
    organization_id: UUID,
    project_id: UUID,
    chat_request: ChatRequest,
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
) -> StreamingResponse | JSONResponse:
    """
    Handles chat operations for a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        chat_request (ChatRequest): The chat request containing the input message.
        use_streaming (bool): Whether to use streaming response.

    Returns:
        JSONResponse: The chat response.
        StreamingResponse: The streaming chat response.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await handle_chat_request(
                organization_id,
                project_id=project_id,
                chat_request=chat_request,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e,
            organization_id,
            chat_request.session_id,
            "generating chat response",
            project_id,
        )


def _handle_error(
    e: Exception,
    organization_id: UUID,
    session_id: str,
    action: str,
    project_id: Optional[UUID] = "no project id",
):
    """ 
    Handles the error by logging it and raising an HTTPException with a suitable status code.

    Args:
        e (Exception): The exception that was raised.
        document_id (UUID): The ID of the document that caused the error.
        project_id (UUID): The ID of the project that caused the error.
        action (str): A string describing the action being attempted (e.g., 'description', 'questions').

    Raises:
        HTTPException: Raises an appropriate HTTP error.
    """

    # Log the error with additional context
    api_logger.error(
        "Error generating %s in chat %s for organization %s in project %s",
        action,
        session_id,
        organization_id,
        project_id,
        exc_info=True,
    )

    if isinstance(e, ResourceNotFoundError):
        api_logger.error(
            e.message,
            exc_info=True,
        )

        return JSONResponse(
            content={
                "error": "ResourceNotFoundError",
                "message": e.message,
                "status_code": status.HTTP_404_NOT_FOUND,
            },
            status_code=status.HTTP_404_NOT_FOUND,
        )

    # Raise a generic 500 error for unexpected failures, preserving the original exception
    return JSONResponse(
        content={
            "error": "InternalServerError",
            "message": f"Error generating {action} for organization {organization_id} in project {project_id}.",
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        },
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )
