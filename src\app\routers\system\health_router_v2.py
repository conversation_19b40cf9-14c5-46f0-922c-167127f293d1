"""
This module defines FastAPI routers for health checks.
"""

# External imports
from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import JSONResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import SpanKind

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v2 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.models.system.health_models_v2 import HealthCheckResponse


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


@api_router.get(
    "/healthcheck",
    description="This endpoint checks the health of the service.",
    operation_id="get_health_check",
    response_description="The health status of the service",
    response_model=HealthCheckResponse,
    summary="Checks the health of the service.",
)
async def get_health_check(request: Request) -> JSONResponse:
    """
    This endpoint checks the health of the service.
    Returns the response as JSONResponse.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            # Simulate service and dependencies status
            service_status = "Healthy"
            dependencies_status = "Healthy"
            app_version = "v0.1.0"

            return JSONResponse(
                content={
                    "service_status": service_status,
                    "dependencies_status": dependencies_status,
                    "app_version": app_version,
                },
                status_code=status.HTTP_200_OK,  # TODO: Issue 20 | Health checks should be properly implemented
            )

    except Exception as e:
        api_logger.error("Error occurred while performing health check.", exc_info=True)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred while performing health check.",
        ) from e
