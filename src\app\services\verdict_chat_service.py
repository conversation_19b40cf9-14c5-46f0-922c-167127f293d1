"""
Service for integrating rechtspraak search with verdict chat functionality.
This service handles the complete flow from verdict search to chat initialization.
"""

import logging
from typing import Optional, Dict, Any
from uuid import UUID

from src.app.services.rechtspraak_search_service import RechtspraakSearchService
from src.app.services.chat.chat_services_v1 import Chat<PERSON>istoryService
from src.app.models.chat.chat_models_v1 import SeedMessageIn


class VerdictChatService:
    """
    Service that combines rechtspraak search with verdict chat functionality.
    Handles the complete flow from verdict ID to initialized chat conversation.
    """

    def __init__(self):
        """Initialize the verdict chat service."""
        self._rechtspraak_service = RechtspraakSearchService()

    async def start_chat_with_verdict(
        self,
        organization_id: UUID,
        user_id: str,
        session_id: str,
        verdict_id: str,
        fetch_full_content: bool = True
    ) -> Dict[str, Any]:
        """
        Start a chat conversation with a specific verdict.
        
        Args:
            organization_id: The organization ID
            user_id: The user ID
            session_id: The chat session ID
            verdict_id: The ECLI verdict ID (e.g., "ECLI:NL:HR:2023:123")
            fetch_full_content: Whether to fetch full verdict content from rechtspraak.nl
            
        Returns:
            Dict containing the seeded message ID and verdict metadata
            
        Raises:
            ValueError: If verdict not found or already seeded
            Exception: If external service calls fail
        """
        try:
            # Step 1: Get verdict content
            if fetch_full_content:
                verdict_content, metadata = await self._fetch_verdict_content(verdict_id)
            else:
                # Use basic content if full fetch is disabled
                verdict_content = f"Verdict {verdict_id} - Content will be retrieved when needed."
                metadata = {"verdict_id": verdict_id, "source": "rechtspraak.nl"}

            # Step 2: Create seed message
            seed_message = SeedMessageIn(
                conversation_id=session_id,
                content=verdict_content,
                role="assistant",
                order=0,
                verdict_id=verdict_id,
                metadata=metadata
            )

            # Step 3: Seed the chat conversation
            chat_history_service = ChatHistoryService(
                organization_id=organization_id,
                user_id=user_id,
                session_id=session_id
            )

            message_id = chat_history_service.add_seed_message(seed_message, user_id)

            logging.info(f"Successfully seeded verdict {verdict_id} in session {session_id}")

            return {
                "message_id": message_id,
                "verdict_id": verdict_id,
                "session_id": session_id,
                "metadata": metadata,
                "content_length": len(verdict_content)
            }

        except ValueError as e:
            # Re-raise validation errors (like duplicate verdicts)
            logging.warning(f"Validation error seeding verdict {verdict_id}: {e}")
            raise
        except Exception as e:
            logging.error(f"Error starting chat with verdict {verdict_id}: {e}", exc_info=True)
            raise

    async def _fetch_verdict_content(self, verdict_id: str) -> tuple[str, Dict[str, Any]]:
        """
        Fetch full verdict content from rechtspraak.nl or other sources.
        
        Args:
            verdict_id: The ECLI verdict ID
            
        Returns:
            Tuple of (content, metadata)
        """
        # TODO: Implement actual rechtspraak.nl API integration
        # For now, return mock content that would come from the actual API
        
        # This would be replaced with actual API calls to:
        # 1. rechtspraak.nl API
        # 2. Your internal verdict database
        # 3. Cached verdict content
        
        mock_content = f"""
{verdict_id}

Hoge Raad der Nederlanden
Datum: 15 januari 2023
Zaaknummer: 22/12345

UITSPRAAK

In de zaak van:
[Appellant], wonende te [Plaats],
appellant,
advocaat: mr. [Naam],

tegen

[Verweerder], wonende te [Plaats],
verweerder,
advocaat: mr. [Naam].

Het geding in cassatie

Appellant is in cassatie gekomen tegen een arrest van het gerechtshof Amsterdam van 15 maart 2022.

BESLISSING

De Hoge Raad:
- verwerpt het beroep in cassatie;
- veroordeelt appellant in de kosten van het geding in cassatie.

Deze uitspraak is gedaan door [Namen rechters] en in het openbaar uitgesproken op 15 januari 2023.
        """.strip()

        metadata = {
            "verdict_id": verdict_id,
            "court": "Hoge Raad",
            "date": "2023-01-15",
            "case_number": "22/12345",
            "case_type": "Civil",
            "source": "rechtspraak.nl",
            "language": "nl",
            "parties": {
                "appellant": "[Appellant]",
                "respondent": "[Verweerder]"
            },
            "outcome": "Appeal rejected",
            "costs_awarded": True,
            "legal_areas": ["Civil Procedure", "Cassation"],
            "fetch_timestamp": "2024-01-01T12:00:00Z"
        }

        return mock_content, metadata

    async def search_and_start_chat(
        self,
        organization_id: UUID,
        user_id: str,
        session_id: str,
        search_query: str,
        legal_areas: Optional[list[str]] = None,
        sub_areas: Optional[list[str]] = None,
        auto_select_first: bool = False
    ) -> Dict[str, Any]:
        """
        Search for verdicts and optionally start a chat with the first result.
        
        Args:
            organization_id: The organization ID
            user_id: The user ID  
            session_id: The chat session ID
            search_query: The search query
            legal_areas: Legal area filters
            sub_areas: Legal sub-area filters
            auto_select_first: Whether to automatically start chat with first result
            
        Returns:
            Dict containing search results and optional chat initialization
        """
        try:
            # Step 1: Search for verdicts
            verdict_ids = await self._rechtspraak_service.search(
                query=search_query,
                legal_areas=legal_areas,
                sub_areas=sub_areas,
                top=10
            )

            result = {
                "search_query": search_query,
                "verdict_ids": verdict_ids,
                "count": len(verdict_ids)
            }

            # Step 2: Optionally start chat with first result
            if auto_select_first and verdict_ids:
                first_verdict_id = verdict_ids[0]
                chat_result = await self.start_chat_with_verdict(
                    organization_id=organization_id,
                    user_id=user_id,
                    session_id=session_id,
                    verdict_id=first_verdict_id
                )
                result["chat_initialized"] = chat_result

            return result

        except Exception as e:
            logging.error(f"Error in search_and_start_chat: {e}", exc_info=True)
            raise

    def get_verdict_summary(self, verdict_id: str, metadata: Dict[str, Any]) -> str:
        """
        Generate a brief summary of a verdict for display purposes.
        
        Args:
            verdict_id: The verdict ID
            metadata: The verdict metadata
            
        Returns:
            A formatted summary string
        """
        court = metadata.get("court", "Unknown Court")
        date = metadata.get("date", "Unknown Date")
        case_type = metadata.get("case_type", "Unknown Type")
        outcome = metadata.get("outcome", "Unknown Outcome")

        return f"""
📋 **Verdict Summary**
🏛️ **Court**: {court}
📅 **Date**: {date}
⚖️ **Type**: {case_type}
✅ **Outcome**: {outcome}
🔗 **ID**: {verdict_id}

This verdict has been added to your conversation. You can now ask questions about it!
        """.strip()

    async def list_seeded_verdicts(
        self,
        organization_id: UUID,
        user_id: str,
        session_id: str
    ) -> list[Dict[str, Any]]:
        """
        List all verdicts that have been seeded in a chat session.
        
        Args:
            organization_id: The organization ID
            user_id: The user ID
            session_id: The chat session ID
            
        Returns:
            List of verdict information dictionaries
        """
        try:
            chat_history_service = ChatHistoryService(
                organization_id=organization_id,
                user_id=user_id,
                session_id=session_id
            )

            chat_history = chat_history_service.get_chat_history()
            seeded_verdicts = []

            for message in chat_history.messages:
                # Check if message is a seeded verdict (has requires_response=False)
                if (hasattr(message, 'additional_kwargs') and 
                    message.additional_kwargs.get('requires_response') is False):
                    
                    # Try to extract verdict information from content
                    content = message.content
                    if content and "ECLI:" in content:
                        # Extract ECLI ID (basic regex)
                        import re
                        ecli_match = re.search(r'ECLI:[A-Z]{2}:[A-Z]+:\d{4}:\d+', content)
                        if ecli_match:
                            verdict_id = ecli_match.group(0)
                            seeded_verdicts.append({
                                "verdict_id": verdict_id,
                                "message_id": message.id,
                                "content_preview": content[:200] + "..." if len(content) > 200 else content,
                                "timestamp": getattr(message, 'timestamp', None)
                            })

            return seeded_verdicts

        except Exception as e:
            logging.error(f"Error listing seeded verdicts: {e}", exc_info=True)
            return []
