"""
Unit tests for seed message functionality in chat services.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from uuid import UUI<PERSON>, uuid4

from src.app.models.chat.chat_models_v1 import SeedMessageIn
from src.app.services.chat.chat_services_v1 import ChatHistoryService
from langchain_core.messages import AIMessage, SystemMessage


class TestSeedMessageFunctionality:
    """Test suite for seed message functionality."""

    @pytest.fixture
    def chat_history_service(self):
        """Create a ChatHistoryService instance for testing."""
        organization_id = uuid4()
        user_id = "test-user-123"
        session_id = "test-session-456"
        
        return ChatHistoryService(
            organization_id=organization_id,
            user_id=user_id,
            session_id=session_id
        )

    @pytest.fixture
    def sample_seed_message(self):
        """Create a sample seed message for testing."""
        return SeedMessageIn(
            conversation_id="test-conversation-123",
            content="This is a test verdict content from rechtspraak.nl",
            role="assistant",
            order=0,
            verdict_id="ECLI:NL:HR:2023:123",
            metadata={"court": "Hoge Raad", "date": "2023-01-15"}
        )

    @pytest.fixture
    def mock_chat_history(self):
        """Create a mock chat history."""
        mock_history = Mock()
        mock_history.messages = []
        mock_history.add_messages = Mock()
        return mock_history

    def test_seed_message_model_validation(self):
        """Test SeedMessageIn model validation."""
        # Valid seed message
        seed = SeedMessageIn(
            conversation_id="test-123",
            content="Test content",
            role="assistant"
        )
        assert seed.conversation_id == "test-123"
        assert seed.content == "Test content"
        assert seed.role == "assistant"
        assert seed.order == 0
        assert seed.verdict_id is None
        assert seed.metadata is None

    def test_seed_message_with_verdict_metadata(self):
        """Test SeedMessageIn with verdict-specific fields."""
        seed = SeedMessageIn(
            conversation_id="test-123",
            content="Verdict content",
            role="assistant",
            verdict_id="ECLI:NL:HR:2023:123",
            metadata={"court": "Hoge Raad", "case_type": "Civil"}
        )
        assert seed.verdict_id == "ECLI:NL:HR:2023:123"
        assert seed.metadata["court"] == "Hoge Raad"
        assert seed.metadata["case_type"] == "Civil"

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.get_chat_history')
    def test_add_seed_message_success(self, mock_get_history, chat_history_service, sample_seed_message, mock_chat_history):
        """Test successful addition of seed message."""
        mock_get_history.return_value = mock_chat_history
        
        result = chat_history_service.add_seed_message(sample_seed_message, "test-user")
        
        # Verify chat history was called correctly
        mock_get_history.assert_called_once()
        mock_chat_history.add_messages.assert_called_once()
        
        # Verify the message was created correctly
        call_args = mock_chat_history.add_messages.call_args
        messages = call_args[0][0]
        assert len(messages) == 1
        assert isinstance(messages[0], AIMessage)
        assert messages[0].content == sample_seed_message.content
        
        # Verify requires_response=False was passed
        kwargs = call_args[1]
        assert kwargs.get('requires_response') is False

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.get_chat_history')
    def test_add_seed_message_system_role(self, mock_get_history, chat_history_service, mock_chat_history):
        """Test adding seed message with system role."""
        mock_get_history.return_value = mock_chat_history
        
        seed = SeedMessageIn(
            conversation_id="test-123",
            content="System message content",
            role="system"
        )
        
        chat_history_service.add_seed_message(seed, "test-user")
        
        # Verify system message was created
        call_args = mock_chat_history.add_messages.call_args
        messages = call_args[0][0]
        assert isinstance(messages[0], SystemMessage)

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.get_chat_history')
    def test_add_seed_message_empty_content_raises_error(self, mock_get_history, chat_history_service, mock_chat_history):
        """Test that empty content raises ValueError."""
        mock_get_history.return_value = mock_chat_history
        
        seed = SeedMessageIn(
            conversation_id="test-123",
            content="   ",  # Only whitespace
            role="assistant"
        )
        
        with pytest.raises(ValueError, match="Seed message content cannot be empty"):
            chat_history_service.add_seed_message(seed, "test-user")

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.get_chat_history')
    def test_duplicate_verdict_prevention(self, mock_get_history, chat_history_service, sample_seed_message, mock_chat_history):
        """Test that duplicate verdicts are prevented."""
        # Create existing message with same content and requires_response=False
        existing_message = Mock()
        existing_message.content = sample_seed_message.content
        existing_message.additional_kwargs = {'requires_response': False}
        
        mock_chat_history.messages = [existing_message]
        mock_get_history.return_value = mock_chat_history
        
        with pytest.raises(ValueError, match="This verdict has already been added to the conversation"):
            chat_history_service.add_seed_message(sample_seed_message, "test-user")

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.get_chat_history')
    def test_duplicate_check_ignores_regular_messages(self, mock_get_history, chat_history_service, sample_seed_message, mock_chat_history):
        """Test that duplicate check ignores regular messages (requires_response=True)."""
        # Create existing message with same content but requires_response=True
        existing_message = Mock()
        existing_message.content = sample_seed_message.content
        existing_message.additional_kwargs = {'requires_response': True}
        
        mock_chat_history.messages = [existing_message]
        mock_get_history.return_value = mock_chat_history
        
        # Should not raise error since existing message requires response
        result = chat_history_service.add_seed_message(sample_seed_message, "test-user")
        assert result is not None

    def test_seed_message_validation_errors(self):
        """Test various validation errors for SeedMessageIn."""
        # Missing required fields
        with pytest.raises(ValueError):
            SeedMessageIn()
        
        # Empty conversation_id
        with pytest.raises(ValueError):
            SeedMessageIn(conversation_id="", content="test", role="assistant")

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.get_chat_history')
    def test_session_id_update(self, mock_get_history, chat_history_service, sample_seed_message, mock_chat_history):
        """Test that session_id and user_id are updated correctly."""
        mock_get_history.return_value = mock_chat_history
        
        chat_history_service.add_seed_message(sample_seed_message, "new-user")
        
        # Verify session_id and user_id were updated
        assert mock_chat_history.session_id == sample_seed_message.conversation_id
        assert mock_chat_history.user_id == "new-user"


class TestSeedMessageIntegration:
    """Integration tests for seed message functionality."""

    def test_verdict_content_formatting(self):
        """Test that verdict content is properly formatted."""
        verdict_content = """
        ECLI:NL:HR:2023:123
        
        Hoge Raad der Nederlanden
        Datum: 15 januari 2023
        
        Uitspraak in de zaak...
        """
        
        seed = SeedMessageIn(
            conversation_id="test-123",
            content=verdict_content.strip(),
            role="assistant",
            verdict_id="ECLI:NL:HR:2023:123"
        )
        
        assert "ECLI:NL:HR:2023:123" in seed.content
        assert seed.verdict_id == "ECLI:NL:HR:2023:123"

    def test_large_verdict_content(self):
        """Test handling of large verdict content."""
        large_content = "A" * 10000  # 10KB of content
        
        seed = SeedMessageIn(
            conversation_id="test-123",
            content=large_content,
            role="assistant"
        )
        
        assert len(seed.content) == 10000
        assert seed.content == large_content
