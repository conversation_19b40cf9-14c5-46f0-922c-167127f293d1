"""
This module defines models for system health operations.
"""

# Standard imports
from typing import Optional

# External imports
from pydantic import BaseModel


# TODO: Issue 19 | External dependencies should be included in health models
class HealthCheckResponse(BaseModel):
    """
    Model for the health check response.
    """

    service_status: Optional[str] = None
    dependencies_status: Optional[str] = None
    app_version: Optional[str] = None
