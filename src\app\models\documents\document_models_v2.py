"""
This module defines models for document context.
"""

# Standard imports
from enum import Enum
from uuid import UUID

# External imports
from pydantic import BaseModel, Field


class DocumentDescriptionResponse(BaseModel):
    """
    Model for the document description response.
    """

    id: UUID = Field(..., example="bf1ff3f9-b116-4da0-ba72-c83c630aee0f")
    project_id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    description: str = Field(
        ...,
        example=(
            "### Document Description\n\n"
            "This document describes the legal case in detail, including all relevant "
            "background information."
        ),
    )
    prompt_tokens: int = Field(..., example=20)
    completion_tokens: int = Field(..., example=30)
    total_tokens: int = Field(..., example=50)


class DocumentQuestionsResponse(BaseModel):
    """
    Model for the document questions response.
    """

    id: UUID = Field(..., example="bf1ff3f9-b116-4da0-ba72-c83c630aee0f")
    project_id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    questions: str = Field(
        ...,
        example=(
            "### Document Questions\n"
            "- What are the main arguments presented in the document?\n"
            "- What evidence supports the claims made in the document?"
        ),
    )
    prompt_tokens: int = Field(..., example=20)
    completion_tokens: int = Field(..., example=30)
    total_tokens: int = Field(..., example=50)


class DocumentStrongPointsResponse(BaseModel):
    """
    Model for the document's strong points response.
    """

    id: UUID = Field(..., example="bf1ff3f9-b116-4da0-ba72-c83c630aee0f")
    project_id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    strengths: str = Field(
        ...,
        example=(
            "### Strong Points\n"
            "- The document provides a comprehensive analysis of the topic.\n"
            "- The arguments presented are well-supported with evidence."
        ),
    )
    prompt_tokens: int = Field(..., example=20)
    completion_tokens: int = Field(..., example=30)
    total_tokens: int = Field(..., example=50)


class DocumentSummaryResponse(BaseModel):
    """
    Model for the document summary response.
    """

    id: UUID = Field(..., example="bf1ff3f9-b116-4da0-ba72-c83c630aee0f")
    project_id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    summary: str = Field(
        ...,
        example=(
            "### Document Summary\n\n"
            "This document summarizes the key points of the legal case."
        ),
    )
    prompt_tokens: int = Field(..., example=20)
    completion_tokens: int = Field(..., example=30)
    total_tokens: int = Field(..., example=50)


class DocumentWeakPointsResponse(BaseModel):
    """
    Model for the document's weak points response.
    """

    id: UUID = Field(..., example="dbe199d4-5dab-4e4f-8103-7499b29269e9")
    project_id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    weaknesses: str = Field(
        ...,
        example=(
            "### Weak Points\n"
            "- The document lacks a clear structure.\n"
            "- The conclusion is not well-developed."
        ),
    )
    prompt_tokens: int = Field(..., example=20)
    completion_tokens: int = Field(..., example=30)
    total_tokens: int = Field(..., example=50)


class DocumentType(Enum):
    """
    Enumeration for the different types of legal documents.
    """

    WRIT_OF_SUMMONS = "WritOfSummons"
    STATEMENT_OF_DEFENCE = "StatementOfDefence"
    STATEMENT_OF_DEFENCE_COUNTERCLAIM = "StatementOfDefenceCounterClaim",
    GOVERNMENT_JUDGMENT = "GovernmentJudgment",
    OBJECTION = "Objection",
    EVIDENCE = "Evidence",
