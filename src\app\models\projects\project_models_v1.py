"""
This module defines models for project context.
"""

# Standard imports
from uuid import UUID

# External imports
from pydantic import BaseModel, Field


class ProjectDescriptionResponse(BaseModel):
    """
    Model for the project description response.
    """

    id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    description: str = Field(
        ...,
        example=(
            "### Project Description\n\n"
            "This project involves the legal analysis of case documents related to a "
            "corporate lawsuit, including all relevant background information."
        ),
    )
    prompt_tokens: int = Field(..., examples=20)
    completion_tokens: int = Field(..., examples=30)
    total_tokens: int = Field(..., example=50)


class ProjectQuestionsResponse(BaseModel):
    """
    Model for the project questions response.
    """

    id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    questions: str = Field(
        ...,
        example=(
            "### Project Questions\n"
            "- What is the current status of the lawsuit?\n"
            "- What are the next legal steps?\n"
            "- Are there any settlement offers on the table?"
        ),
    )
    prompt_tokens: int = Field(..., examples=10)
    completion_tokens: int = Field(..., examples=20)
    total_tokens: int = Field(..., example=30)


class ProjectStrongPointsResponse(BaseModel):
    """
    Model for the project analysis (strong points) response.
    """

    id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    strengths: str = Field(
        ...,
        example=(
            "### Strong Points\n"
            "- The document provides a comprehensive analysis of the topic.\n"
            "- The arguments presented are well-supported with evidence."
        ),
    )
    prompt_tokens: int = Field(..., examples=20)
    completion_tokens: int = Field(..., examples=40)
    total_tokens: int = Field(..., example=60)


class ProjectSummaryResponse(BaseModel):
    """
    Model for the project summary response.
    """

    id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    summary: str = Field(
        ...,
        example=(
            "### Project Summary\n\n"
            "This project involves the legal analysis of case documents related to a "
            "corporate lawsuit. The analysis includes reviewing contracts, correspondence, "
            "and court filings to identify key issues and potential outcomes."
        ),
    )
    prompt_tokens: int = Field(..., examples=20)
    completion_tokens: int = Field(..., examples=40)
    total_tokens: int = Field(..., example=60)


class ProjectWeakPointsResponse(BaseModel):
    """
    Model for the project analysis (weak points) response.
    """

    id: UUID = Field(..., example="550e8400-e29b-41d4-a716-************")
    organization_id: UUID = Field(..., example="f38a52cd-b038-4092-b299-4ea72ab8cc22")
    weaknesses: str = Field(
        ...,
        example=(
            "### Weak Points\n"
            "- The document lacks a clear structure.\n"
            "- The conclusion is not well-developed."
        ),
    )
    prompt_tokens: int = Field(..., examples=20)
    completion_tokens: int = Field(..., examples=40)
    total_tokens: int = Field(..., example=60)
