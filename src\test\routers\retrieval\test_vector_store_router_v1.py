"""
Tests for the vector store router endpoints.
"""

import uuid
from http import HTT<PERSON>tatus
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest
from fastapi import HTTPException
from azure.core.exceptions import ResourceNotFoundError

from src.app.models.retrieval.vector_store_models_v1 import (
    ProjectDocumentsDeleteRequest,
    DocumentToDelete,
)

from src.app.routers.retrieval.vector_store_router_v1 import delete_project_documents


@pytest.fixture
def mock_request():
    """Mock FastAPI request object"""
    return Mock(
        scope={"endpoint": Mock(__name__="delete_project_documents")}, headers={}
    )


@pytest.fixture
def sample_request_body():
    """Sample request body for document deletion"""
    return ProjectDocumentsDeleteRequest(
        documents=[
            DocumentToDelete(document_id=uuid.uuid4(), file_name="doc1.pdf"),
            DocumentToDelete(document_id=uuid.uuid4(), file_name="doc2.pdf"),
        ]
    )


@pytest.mark.asyncio
async def test_delete_project_documents_successful(mock_request, sample_request_body):
    """Test successful deletion of multiple documents"""
    organization_id = uuid.uuid4()
    project_id = uuid.uuid4()

    # Mock VectorStoreService
    with patch(
        "src.app.routers.retrieval.vector_store_router_v1.VectorStoreService"
    ) as mock_service:
        mock_instance = AsyncMock()
        mock_service.return_value.__aenter__.return_value = mock_instance

        response = await delete_project_documents(
            request=mock_request,
            organization_id=organization_id,
            project_id=project_id,
            request_body=sample_request_body,
        )

        # Verify response
        assert response.status_code == 200
        response_data = response.body.decode()
        assert "successful_deletions" in response_data
        assert "failed_deletions" in response_data
        assert len(mock_instance.delete_document.mock_calls) == 2


@pytest.mark.asyncio
async def test_delete_project_documents_partial_failure(
    mock_request, sample_request_body
):
    """Test partial failure when deleting multiple documents"""
    organization_id = uuid.uuid4()
    project_id = uuid.uuid4()

    with patch(
        "src.app.routers.retrieval.vector_store_router_v1.VectorStoreService"
    ) as mock_service:
        mock_instance = AsyncMock()
        # Make first deletion succeed and second fail
        mock_instance.delete_document.side_effect = [
            None,
            ResourceNotFoundError("Document not found"),
        ]
        mock_service.return_value.__aenter__.return_value = mock_instance

        response = await delete_project_documents(
            request=mock_request,
            organization_id=organization_id,
            project_id=project_id,
            request_body=sample_request_body,
        )

        # Verify response
        assert response.status_code == 200
        response_data = response.body.decode()
        assert "successful_deletions" in response_data
        assert "failed_deletions" in response_data
        assert "Document not found" in response_data


@pytest.mark.asyncio
async def test_delete_project_documents_complete_failure(
    mock_request, sample_request_body
):
    """Test complete failure when deleting multiple documents"""
    organization_id = uuid.uuid4()
    project_id = uuid.uuid4()

    with patch(
        "src.app.routers.retrieval.vector_store_router_v1.VectorStoreService"
    ) as mock_service:
        mock_instance = AsyncMock()
        mock_instance.delete_document.side_effect = Exception("Service unavailable")
        mock_service.return_value.__aenter__.return_value = mock_instance

        response = await delete_project_documents(
            request=mock_request,
            organization_id=organization_id,
            project_id=project_id,
            request_body=sample_request_body,
        )

        # Verify response
        assert response.status_code == 200
        response_data = response.body.decode()
        assert "failed_deletions" in response_data
        assert len(mock_instance.delete_document.mock_calls) == 2
        assert "Service unavailable" in response_data


@pytest.mark.asyncio
async def test_delete_project_documents_empty_list():
    """Test deletion with empty document list"""
    organization_id = uuid.uuid4()
    project_id = uuid.uuid4()
    empty_request = ProjectDocumentsDeleteRequest(documents=[])

    # Mock VectorStoreService
    with patch(
        "src.app.routers.retrieval.vector_store_router_v1.VectorStoreService"
    ) as mock_service:
        mock_instance = AsyncMock()
        mock_service.return_value.__aenter__.return_value = mock_instance

        response = await delete_project_documents(
            request=Mock(
                scope={"endpoint": Mock(__name__="delete_project_documents")},
                headers={},
            ),
            organization_id=organization_id,
            project_id=project_id,
            request_body=empty_request,
        )

        # Verify response
        assert response.status_code == 200
        assert "successful_deletions" in response.body.decode()
        assert "failed_deletions" in response.body.decode()
        # Verify no delete calls were made
        assert mock_instance.delete_document.call_count == 0


@pytest.mark.asyncio
async def test_delete_project_documents_service_error(
    mock_request, sample_request_body
):
    """Test handling of service-level errors"""
    organization_id = uuid.uuid4()
    project_id = uuid.uuid4()

    with patch(
        "src.app.routers.retrieval.vector_store_router_v1.VectorStoreService"
    ) as mock_service:
        mock_instance = AsyncMock()
        mock_instance.vector_store_exists.return_value = True
        mock_instance.delete_document.side_effect = Exception(
            "Error deleting documents"
        )
        mock_service.return_value.__aenter__.return_value = mock_instance
        response = await delete_project_documents(
            request=mock_request,
            organization_id=organization_id,
            project_id=project_id,
            request_body=sample_request_body,
        )
        assert response.status_code == 200
        assert "Error deleting documents" in response.body.decode()
