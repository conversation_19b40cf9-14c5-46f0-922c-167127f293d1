"""
This module defines services for indexing operations.
"""

# Standard imports
from abc import ABC, abstractmethod
from typing import List
from uuid import UUID

# External imports
from azure.core import MatchConditions
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import ResourceNotFoundError
from azure.search.documents.indexes.aio import SearchIndexClient
from azure.search.documents.indexes.models import SearchField
from langchain_core.documents import Document
from langchain_community.vectorstores.azuresearch import AzureSearch
from langchain_openai import AzureOpenAIEmbeddings


class IndexService(ABC):
    """
    Abstract base class for vector index services.
    """

    @abstractmethod
    async def initialize(self, project_id: UUID, fields: List[SearchField]) -> None:
        """
        Initializes the search index for a specific project.

        Args:
            project_id (UUID): The project identifier
            fields (List[SearchField]): List of fields for the index schema
        """

    @abstractmethod
    async def index_documents(self, documents: List[Document]) -> bool:
        """
        Indexes a batch of documents into the vector store.

        Args:
            documents (List[Document]): Documents to be indexed

        Returns:
            bool: True if indexing was successful, False otherwise
        """

    @abstractmethod
    async def delete_documents(self, document_id: UUID) -> int:
        """
        Deletes all documents with the specified document_id.

        Args:
            document_id (UUID): The document identifier to delete

        Returns:
            int: Number of documents deleted
        """

    @abstractmethod
    async def delete_index(self, project_id: UUID) -> None:
        """
        Deletes the entire index for a project.

        Args:
            project_id (UUID): The project identifier
        """

    @abstractmethod
    async def index_exists(self, project_id: UUID) -> bool:
        """
        Checks if an index exists for the specified project.

        Args:
            project_id (UUID): The project identifier

        Returns:
            bool: True if index exists, False otherwise
        """

    @abstractmethod
    async def close(self) -> None:
        """
        Closes all client connections and cleans up resources.
        """


class AiSearchIndexService(IndexService):
    """
    Implementation of IndexService for Azure AI Search.
    """

    def __init__(
        self,
        search_endpoint: str,
        search_admin_key: str,
        embeddings: AzureOpenAIEmbeddings,
        index_prefix: str = "idx-",
    ) -> None:
        """
        Initializes the AiSearchIndexService with connection parameters.

        Args:
            search_endpoint (str): Azure Search endpoint URL
            search_admin_key (str): Azure Search admin API key
            embeddings (AzureOpenAIEmbeddings): Initialized embeddings client
            index_prefix (str, optional): Prefix for index names. Defaults to "idx-".
        """

        self.search_endpoint = search_endpoint
        self.search_admin_key = search_admin_key
        self.embeddings = embeddings
        self.index_prefix = index_prefix
        self._client: AzureSearch | None = None
        self._index_client: SearchIndexClient | None = None
        self._project_id: UUID | None = None

    async def initialize(self, project_id: UUID, fields: List[SearchField]) -> None:
        """
        Initializes the search index for a specific project.

        Args:
            project_id (UUID): The project identifier
            fields (List[SearchField]): List of fields for the index schema
        """

        self._project_id = project_id
        index_name = f"{self.index_prefix}{project_id}"

        self._client = AzureSearch(
            azure_search_endpoint=self.search_endpoint,
            azure_search_key=self.search_admin_key,
            index_name=index_name,
            fields=fields,
            embedding_function=self.embeddings.embed_query,
        )

        self._index_client = SearchIndexClient(
            endpoint=self.search_endpoint,
            credential=AzureKeyCredential(self.search_admin_key),
        )

    async def index_documents(self, documents: List[Document]) -> list[str]:
        """
        Indexes a batch of documents into the vector store.

        Args:
            documents (List[Document]): Documents to be indexed

        Returns:
            bool: True if indexing was successful, False otherwise
        """

        if not self._client:
            raise RuntimeError("Index not initialized.")

        return self._client.add_documents(documents)

    async def delete_documents(self, document_id: UUID) -> int:
        """
        Deletes all documents with the specified document_id.

        Args:
            document_id (UUID): The document identifier to delete

        Returns:
            int: Number of documents deleted
        """

        if not self._client:
            raise RuntimeError("Index not initialized.")

        documents = await self._client.asimilarity_search(
            query="*",
            filters=f"document_id eq '{str(document_id)}'",
            k=100,
        )

        ids_to_delete = [
            doc.metadata["id"] for doc in documents if "id" in doc.metadata
        ]

        if ids_to_delete:
            await self._client.adelete(ids=ids_to_delete)

        return len(ids_to_delete)

    async def delete_index(self, project_id: UUID) -> None:
        """
        Deletes the entire index for a project.

        Args:
            project_id (UUID): The project identifier
        """

        if not self._index_client:
            self._index_client = SearchIndexClient(
                endpoint=self.search_endpoint,
                credential=AzureKeyCredential(self.search_admin_key),
            )

        await self._index_client.delete_index(
            f"{self.index_prefix}{project_id}",
            match_condition=MatchConditions.Unconditionally,
        )

    async def index_exists(self, project_id: UUID) -> bool:
        """
        Checks if an index exists for the specified project.

        Args:
            project_id (UUID): The project identifier

        Returns:
            bool: True if index exists, False otherwise
        """

        if not self._index_client:
            self._index_client = SearchIndexClient(
                endpoint=self.search_endpoint,
                credential=AzureKeyCredential(self.search_admin_key),
            )

        try:
            await self._index_client.get_index(f"{self.index_prefix}{project_id}")
            return True

        except ResourceNotFoundError:
            return False

    async def close(self) -> None:
        """
        Closes all client connections and cleans up resources.
        """

        if self._index_client:
            await self._index_client.close()

        if self._client and self._client.client:
            self._client.client.close()
