"""
Integration tests for the seed message API endpoint.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock
from uuid import uuid4
import json

from src.app.main import app
from src.app.models.chat.chat_models_v1 import SeedMessageIn


class TestSeedMessageAPI:
    """Integration tests for the /chat/messages/seed endpoint."""

    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)

    @pytest.fixture
    def organization_id(self):
        """Sample organization ID."""
        return str(uuid4())

    @pytest.fixture
    def sample_seed_payload(self):
        """Sample seed message payload."""
        return {
            "conversation_id": "test-conversation-123",
            "content": "ECLI:NL:HR:2023:123\n\nHoge Raad der Nederlanden\nDatum: 15 januari 2023\n\nUitspraak in de zaak...",
            "role": "assistant",
            "order": 0,
            "verdict_id": "ECLI:NL:HR:2023:123",
            "metadata": {
                "court": "Hoge Raad",
                "date": "2023-01-15",
                "case_type": "Civil"
            }
        }

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message')
    def test_seed_message_success(self, mock_add_seed, client, organization_id, sample_seed_payload):
        """Test successful seed message creation."""
        mock_add_seed.return_value = "message-id-123"
        
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            params={"user_id": "test-user-123"},
            json=sample_seed_payload
        )
        
        assert response.status_code == 201
        assert response.json() == {"id": "message-id-123"}
        
        # Verify the service was called with correct parameters
        mock_add_seed.assert_called_once()
        call_args = mock_add_seed.call_args
        seed_message = call_args[0][0]
        user_id = call_args[0][1]
        
        assert isinstance(seed_message, SeedMessageIn)
        assert seed_message.conversation_id == sample_seed_payload["conversation_id"]
        assert seed_message.content == sample_seed_payload["content"]
        assert seed_message.verdict_id == sample_seed_payload["verdict_id"]
        assert user_id == "test-user-123"

    def test_seed_message_missing_required_fields(self, client, organization_id):
        """Test seed message with missing required fields."""
        incomplete_payload = {
            "conversation_id": "test-123"
            # Missing content and role
        }
        
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            params={"user_id": "test-user-123"},
            json=incomplete_payload
        )
        
        assert response.status_code == 422  # Validation error

    def test_seed_message_invalid_organization_id(self, client, sample_seed_payload):
        """Test seed message with invalid organization ID."""
        response = client.post(
            "/invalid-uuid/chat/messages/seed",
            params={"user_id": "test-user-123"},
            json=sample_seed_payload
        )
        
        assert response.status_code == 422  # Validation error

    def test_seed_message_missing_user_id(self, client, organization_id, sample_seed_payload):
        """Test seed message without user_id parameter."""
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            json=sample_seed_payload
        )
        
        assert response.status_code == 422  # Missing required parameter

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message')
    def test_seed_message_duplicate_verdict_error(self, mock_add_seed, client, organization_id, sample_seed_payload):
        """Test handling of duplicate verdict error."""
        mock_add_seed.side_effect = ValueError("This verdict has already been added to the conversation.")
        
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            params={"user_id": "test-user-123"},
            json=sample_seed_payload
        )
        
        assert response.status_code == 500  # Internal server error
        response_data = response.json()
        assert "error" in response_data
        assert "InternalServerError" in response_data["error"]

    @patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message')
    def test_seed_message_service_exception(self, mock_add_seed, client, organization_id, sample_seed_payload):
        """Test handling of service exceptions."""
        mock_add_seed.side_effect = Exception("Database connection failed")
        
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            params={"user_id": "test-user-123"},
            json=sample_seed_payload
        )
        
        assert response.status_code == 500

    def test_seed_message_minimal_payload(self, client, organization_id):
        """Test seed message with minimal required fields."""
        minimal_payload = {
            "conversation_id": "test-123",
            "content": "Minimal verdict content",
            "role": "assistant"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "message-id-456"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "test-user-123"},
                json=minimal_payload
            )
            
            assert response.status_code == 201
            assert response.json() == {"id": "message-id-456"}

    def test_seed_message_system_role(self, client, organization_id):
        """Test seed message with system role."""
        system_payload = {
            "conversation_id": "test-123",
            "content": "System message about the verdict",
            "role": "system"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "system-message-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "test-user-123"},
                json=system_payload
            )
            
            assert response.status_code == 201

    def test_seed_message_large_content(self, client, organization_id):
        """Test seed message with large content."""
        large_content = "A" * 50000  # 50KB content
        large_payload = {
            "conversation_id": "test-123",
            "content": large_content,
            "role": "assistant"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "large-message-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "test-user-123"},
                json=large_payload
            )
            
            assert response.status_code == 201

    def test_seed_message_special_characters(self, client, organization_id):
        """Test seed message with special characters and unicode."""
        special_payload = {
            "conversation_id": "test-123",
            "content": "Verdict with special chars: €, ñ, 中文, emoji 🏛️, quotes \"test\", newlines\nand tabs\t",
            "role": "assistant",
            "verdict_id": "ECLI:NL:HR:2023:123"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "special-message-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "test-user-123"},
                json=special_payload
            )
            
            assert response.status_code == 201

    def test_seed_message_with_metadata(self, client, organization_id):
        """Test seed message with complex metadata."""
        metadata_payload = {
            "conversation_id": "test-123",
            "content": "Verdict content",
            "role": "assistant",
            "verdict_id": "ECLI:NL:HR:2023:123",
            "metadata": {
                "court": "Hoge Raad",
                "date": "2023-01-15",
                "case_type": "Civil",
                "parties": ["Plaintiff A", "Defendant B"],
                "legal_areas": ["Contract Law", "Tort Law"],
                "nested": {
                    "key": "value",
                    "number": 42
                }
            }
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "metadata-message-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "test-user-123"},
                json=metadata_payload
            )
            
            assert response.status_code == 201
            
            # Verify metadata was passed correctly
            call_args = mock_add_seed.call_args
            seed_message = call_args[0][0]
            assert seed_message.metadata["court"] == "Hoge Raad"
            assert seed_message.metadata["nested"]["number"] == 42


class TestSeedMessageValidation:
    """Test validation logic for seed messages."""

    def test_empty_content_validation(self):
        """Test that empty content is rejected."""
        with pytest.raises(ValueError):
            SeedMessageIn(
                conversation_id="test-123",
                content="",
                role="assistant"
            )

    def test_whitespace_only_content(self):
        """Test that whitespace-only content is handled."""
        # This should be valid at model level, but rejected at service level
        seed = SeedMessageIn(
            conversation_id="test-123",
            content="   \n\t   ",
            role="assistant"
        )
        assert seed.content == "   \n\t   "

    def test_invalid_role_validation(self):
        """Test role validation."""
        # The model should accept any string role, validation happens at service level
        seed = SeedMessageIn(
            conversation_id="test-123",
            content="Test content",
            role="invalid_role"
        )
        assert seed.role == "invalid_role"
