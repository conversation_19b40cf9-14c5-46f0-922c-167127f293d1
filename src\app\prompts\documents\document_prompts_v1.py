"""
This module contains prompt templates for the documents.
"""

DOCUMENT_DESCRIPTION_PROMPT = """
    # Context
    Consider the following document: {context} \n
    
    # Task
    Give a clear, concise description of what the document is about in a paragraph of at most 3 sentences. Avoid detailed explanations and summaries of content and focus on providing a high-level, descriptive overview of the document.
"""

#TODO: add desired symmary length as an input parameter, either based on user input or based on document metadata:
# LLMS cannot estimate wordcounts properly, so we need to tell it: "this document is 2000 words, summarize it in 200."
DOCUMENT_SUMMARY_GUIDELINES = """
    Your summary must satisfy the following guidelines:
        - Length: the summary should be as long as needed to give an adequate representation of the original material. 
        - Logical Structure: Organize the summary in a coherent manner, often following the structure of the original text.
        - Conciseness: Be brief and to the point, avoiding unnecessary details or examples that are not essential to understanding the main concepts.    
        - No Direct Quotations: Paraphrase the content instead of using direct quotes, unless it’s a key phrase that 
        is particularly significant.
"""

DOCUMENT_SUMMARY_MAIN_ELEMENTS = """
    Give a summary of this document that contains the following elements:
    - Main Idea: Clearly state the central theme, claim or thesis of the original material.
    - Key Points: Highlight the most important claims, arguments, findings and events, focusing on those that support the main idea.
    - Relevant Law: Mention the most important cited statutes, case law, or legal principles that are crucial to the argument or decision. 
"""

# TODO: implement distinct logic for 1 vs multiple documents
DOCUMENT_SUMMARY_PROMPT = f"""
    # Context
    Consider the following document: {{context}} \n
    
    # Task 
    {DOCUMENT_SUMMARY_MAIN_ELEMENTS}
    
    {DOCUMENT_SUMMARY_GUIDELINES}
"""

# TODO: this one should only apply to cases with multiple documents
MULTIPLE_DOCUMENTS_SUMMARY_PROMPT = f"""
    # Context 
    Consider the following document(s): {{context}} \n
    
    # Task 
    {DOCUMENT_SUMMARY_MAIN_ELEMENTS}
    
    If there are multiple documents, also include the following elements:
    - Documents structure: briefly explain the overall structure of how the individual documents relate to each other and how each document contributes to presenting the main theme.  
    - Inconsistencies: when applicable, highlight any inconsistencies or divergences between the factual claims found throughout the documents. 
    - Reading order: suggest a reading order that prioritizes documents that are the most useful or important to get 
    an overall picture of the presented material. 
    - Always include a reference to the original document where important claims, facts, events or statements can be 
    found. 
 
    {DOCUMENT_SUMMARY_GUIDELINES}
"""

# TODO: overhaul the entire logic used here: the idea is to create a timeline  by going through the document piecewise
#  and returning events in json format.
DOCUMENT_TIMELINE_PROMPT = """
Generate a timeline based on all the documents provided so far.
    Only include the timeline. No summaries or other background information.
    The timeline should capture significant milestones relevant to the legal process, including (if any):
    Look for the following types of events:
    - **Documents Sent**: Record the dates when documents were sent to the opposing party or the court.
    - **Contract Signed**: Document the date when contracts were signed by the parties involved.
    - **Writ of Summons Sent**: Include the date and circumstances when the writ of summons was sent.
    - **Attempt to Resolve Conflict**: Note the dates of any attempts to resolve the conflict outside of court.
    - **Deadlines**: Record the dates of any deadlines set by the court or agreed upon by the parties.
    - **Court Hearings**: Include the dates of any court hearings or scheduled appearances.
    - **Payments Made**: Document the dates when payments were made or received.
    - **Other Relevant Events**: Include any other significant events that are described in the documents.
    
    For all events include the following
    - **Date of the event**: Include the date of the event.
    - **Description**: Briefly describe what the event entails.
    - **Significance**: Explain the significance.

    The timeline should be chronological, clear and organized, allowing for easy access to important information. 
    The content of the documents is as follows: {context}
    **Formatting:**
    - All answers must be markdown-compatible. If answer naturally contain tabular information, present it using a markdown table for clarity and organization.
    - All responses should always be in Dutch.
    - All headers and titles should be in Dutch.
"""

# TODO: remove the remaining prompts
DOCUMENT_QUESTIONS_PROMPT = """
    Analyze the provided document content and formulate specific questions related to these document that could be helpful. The context of the document content (which may be part of a case or legal issue) is as follows: {context}. 
    Ensure the questions are aimed at clarifying any ambiguities, gathering additional information, and finding gaps of information in the document.
    
    Analyze all the provided documents and formulate a list of strategic, specific, and actionable questions. These questions should focus on the following goals:
    - Clarify ambiguities: Identify and fill any gaps in the information to ensure a complete understanding of the document.
    - Address key legal issues: Ensure the questions tackle the core legal issues in the case (e.g., contractual obligations, financial agreements, breaches, etc.).
    - Contextual information: Ask for broader context when necessary to fully understand the background of the legal issue or conflict.
    - Logical structure: Begin with broad, contextual questions and progressively move towards more detailed, specific ones.

    The goal is to ensure that the lawyer gathers all the necessary information and knows where to go for additional information.
    **Formatting:**
    - All answers must be markdown-compatible. If answer naturally contain tabular information, present it using a markdown table for clarity and organization.
    - All responses should always be in Dutch.
    - All headers and titles should be in Dutch.
"""

DOCUMENT_STRONG_POINTS_PROMPT = """
    Analyze the provided document content and list the strong points specific to this document. The context of the document content (which may be part of a case or legal issue) is as follows: {context}. 
    Highlight all the favorable facts, evidence, and arguments that support the position presented in the document. Content must be markdown compatible.
"""

DOCUMENT_WEAK_POINTS_PROMPT = """
    Analyze the provided document content and list the weak points specific to this document. The context of the document content (which may be part of a case or legal issue) is as follows: {context}. 
    Identify any potential vulnerabilities, inconsistencies, or unfavorable evidence that could weaken the position presented in the document. Content must be markdown compatible.
"""
