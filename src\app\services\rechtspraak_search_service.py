from __future__ import annotations
from azure.core.exceptions import HttpResponseError
from azure.search.documents.aio import SearchClient
from azure.search.documents.models import QueryType
from azure.core.credentials import AzureKeyCredential
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from openai import OpenAIError
from typing import List, Optional

from src.app.prompts.chat.chat_prompts_v1 import (
    PROMPT_TO_CREATE_QUERY_FOR_SEMANTIC_JURISPRUDENCE_SEARCH_BASED_ON_USER_INPUT,
    PROMPT_TO_CREATE_QUERY_FOR_SEMANTIC_JURISPRUDENCE_SEARCH_BASED_ON_PROJECT_CONTEXT
)
from src.app.prompts.system.system_prompts_v1 import SEMANTIC_SEARCH_FOR_JURISPRUDENCE_PROMPT_CREATION_SYSTEM_PROMPT

import os
import logging


# TODO: add functionality so that when this service is initiated within the context of a project, it will retrieve
#  the relevant project context from the cosmodb using the CosmosDBClient. This project context can then be included
#  together with a user request to perform a search for relevant case law

class RechtspraakSearchService:
    """Thin service layer so routers don't touch the SDK directly."""

    def __init__(self, project_context_str: Optional[str] = None):
        self.project_context_str = project_context_str
        self._init_variables()
        self._search_client = SearchClient(endpoint=self._search_endpoint,
                                           index_name=self._index_name,
                                           credential=AzureKeyCredential(self._search_endpoint_key))
        self._init_chat_client()

    def _init_variables(self) -> None:
        # Variables for azure search endpoint
        self._search_endpoint = os.getenv("AZURE_SEARCH_ENDPOINT")
        self._search_endpoint_key = os.getenv("AZURE_SEARCH_ADMIN_KEY")
        self._index_name = "idx-rechtspraak"

        # Variables for supporting chat client
        self._openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self._openai_api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        self._openai_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        self._openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self._openai_model_name = os.getenv("AZURE_OPENAI_MODEL_NAME")
        self._openai_temperature = float(
            os.getenv("AZURE_OPENAI_TEMPERATURE_CHAT", "0.5")
        )
        self._openai_top_p = float(os.getenv("AZURE_OPENAI_TOP_P_CHAT", "0.5"))
        self._search_top_k = int(os.getenv("AZURE_SEARCH_TOP_K_RESULTS_CHAT", "5"))
        if not (self._search_endpoint and self._search_endpoint_key):
            raise RuntimeError("AZURE_SEARCH_ENDPOINT / AZURE_SEARCH_ADMIN_KEY env-vars must be set")

    def _init_chat_client(self) -> None:
        self._chat_client = self._chat_client = AzureChatOpenAI(
            azure_deployment=self._openai_deployment,
            api_key=self._openai_api_key,
            api_version=self._openai_api_version,
            model=self._openai_model_name,
            temperature=self._openai_temperature,
            top_p=self._openai_top_p,
        )

    async def _create_semantic_search_query(self, query: str) -> (str, dict):
        """"
        Use AI to create a query that is specialized for semantic search, based on the provided use request and
        the project context if available

        Returns:
            str: the query to be used in semantic search through the vector store with case law
            dict: token usage from creating the query
        """

        if self.project_context_str is not None:
            prompt = PROMPT_TO_CREATE_QUERY_FOR_SEMANTIC_JURISPRUDENCE_SEARCH_BASED_ON_PROJECT_CONTEXT.format(
                project_context=self.project_context, user_message=query
            )
        else:
            prompt = PROMPT_TO_CREATE_QUERY_FOR_SEMANTIC_JURISPRUDENCE_SEARCH_BASED_ON_USER_INPUT.format(
                user_message=query)

        response = self._chat_client([
            SystemMessage(content=SEMANTIC_SEARCH_FOR_JURISPRUDENCE_PROMPT_CREATION_SYSTEM_PROMPT),
            HumanMessage(content=prompt)
        ])

        semantic_search_query = response.content
        token_usage = response.usage_metadata
        return semantic_search_query, token_usage

    async def search(
            self,
            query: str,
            legal_areas: Optional[List[str]] = None,
            sub_areas: Optional[List[str]] = None,
            top: int = 10,
    ) -> List[str]:
        """
        Execute a semantic search against Azure AI Search and return verdict IDs.
        """
        filter_expr = self._build_filter(legal_areas, sub_areas)

        try:
            # Try semantic search first. If this fails fall back to simple keyword search
            semantic_search_query, token_usage = await self._create_semantic_search_query(query=query)
            results = await self._search_client.search(
                search_text=semantic_search_query,
                filter=filter_expr,
                top=top,
                query_type=QueryType.SEMANTIC,
                semantic_configuration_name="rechtspraak_search_semantic_configuration",
                select=["id"],  # return only the primary key
            )
        except (OpenAIError, HttpResponseError) as e:
            logging.error("Semantic search failed with error message: %s", e)
            results = await self._search_client.search(
                search_text=query,
                filter=filter_expr,
                top=top,
                query_type=QueryType.SIMPLE,
                select=["id"],  # return only the primary key
            )

        return [doc["id"] async for doc in results]

    @staticmethod
    def _build_filter(
            legal_areas: Optional[List[str]], sub_areas: Optional[List[str]]
    ) -> str | None:
        """
        Translate multiselect filters into an OData filter expression that
        Azure AI Search understands.
        """
        clauses: List[str] = []

        if legal_areas:
            escaped_areas = [area.replace("'", "''") for area in legal_areas]
            area_clauses = [f"legal_area eq '{area}'" for area in escaped_areas]
            clauses.append(f"({' or '.join(area_clauses)})")

        if sub_areas:
            escaped_subs = [area.replace("'", "''") for area in sub_areas]
            sub_clauses = [f"legal_sub_area eq '{sub}'" for sub in escaped_subs]
            clauses.append(f"({' or '.join(sub_clauses)})")

        if clauses:
            # Join with AND so both area and sub-area must match if provided
            return " and ".join(clauses)

        return None


