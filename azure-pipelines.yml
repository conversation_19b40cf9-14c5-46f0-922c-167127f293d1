trigger:
  branches:
    include:
      - main
      - feature/*

resources:
- repo: self

variables:
  appNameDev: 'app-statuta-rag-dev'
  appNamePrd: 'app-statuta-rag-prd'
  azureSubscription: 'AI Innovations (611c3c26-7196-4cb1-8fa4-014a15201c74)'
  dockerRegistryServiceConnectionDev: '************************************'
  dockerRegistryServiceConnectionPrd: '************************************'
  imageRepository: 'statuta/rag-api'
  containerRegistryDev: 'acrstatutashddev.azurecr.io'
  containerRegistryPrd: 'acrstatutashdprd.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.BuildNumber)'
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: CI Stage
  jobs:
  - job: Test
    displayName: Run Pytest
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: UsePythonVersion@0
      displayName: Set up Python
      inputs:
        versionSpec: '3.12'
        addToPath: true
    
    - script: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pytest ./src/test --maxfail=1 --disable-warnings -q --doctest-modules --junitxml=junit/test-results.xml --cov=. --cov-report=xml
      displayName: Run Pytest

  - job: Build
    displayName: Build and Push
    dependsOn: Test
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: Docker@2
      displayName: Build and push an image to container registry
      inputs:
        buildContext: '$(Build.SourcesDirectory)'
        command: 'buildAndPush'
        containerRegistry: '$(dockerRegistryServiceConnectionDev)'
        repository: '$(imageRepository)'
        Dockerfile: '$(dockerfilePath)'
        tags: |
          $(tag)
          latest