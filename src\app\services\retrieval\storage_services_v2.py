"""
This module defines storage services for blobs.
"""

# Standard imports
from abc import ABC, abstractmethod
from typing import Any, AsyncIterable, Dict, List, Optional

# External imports
from azure.core.exceptions import ResourceNotFoundError, HttpResponseError
from azure.storage.blob.aio import BlobServiceClient, BlobClient
from azure.storage.blob import BlobProperties


class StorageService(ABC):
    """
    Abstract base class for storage services.

    This class defines the interface for interacting with different storage providers
    (e.g., Azure Blob Storage, AWS S3, etc.).
    """

    @abstractmethod
    async def delete_blob(self, container_name: str, blob_name: str) -> None:
        """
        Deletes a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to delete.
        """

    @abstractmethod
    async def download_blob(self, container_name: str, blob_name: str) -> bytes:
        """
        Downloads the content of a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to download.

        Returns:
            bytes: The content of the blob.
        """

    @abstractmethod
    async def get_blob_properties(
        self, container_name: str, blob_name: str
    ) -> Dict[str, Any]:
        """
        Retrieves the properties of a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob.

        Returns:
            Dict[str, Any]: A dictionary containing the blob's properties.
        """

    @abstractmethod
    async def get_blob_metadata(
        self, container_name: str, blob_name: str
    ) -> Dict[str, Any]:
        """
        Retrieves the metadata of a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob.

        Returns:
            Dict[str, Any]: A dictionary containing the metadata key-value pairs of the blob.
        """

    @abstractmethod
    async def list_blobs(
        self, container_name: str, prefix: str = ""
    ) -> AsyncIterable[Dict]:
        """
        Lists blobs in a container with an optional prefix.

        Args:
            container_name (str): The name of the container.
            prefix (str, optional): The prefix to filter blobs. Defaults to "".

        Returns:
            AsyncIterable[Dict]: An async iterable of blob properties.
        """

    @abstractmethod
    async def upload_blob(
        self,
        container_name: str,
        blob_name: str,
        content: bytes,
        metadata: Optional[Dict[str, str]] = None,
    ) -> None:
        """
        Uploads a blob with the given content and metadata.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to upload.
            content (bytes): The content of the blob.
            metadata (Dict[str, str], optional): The metadata to associate with the blob.
        """


class BlobStorageService(StorageService):
    """
    Implementation of StorageService for Azure Blob Storage.
    """

    def __init__(self, storage_account_name: str, storage_account_key: str) -> None:
        """
        Initializes the BlobStorageService with the given storage account credentials.

        Args:
            storage_account_name (str): The name of the Azure Storage Account.
            storage_account_key (str): The access key for the Azure Storage Account.
        """

        self._service_client = BlobServiceClient(
            account_url=f"https://{storage_account_name}.blob.core.windows.net",
            credential=storage_account_key,
        )

    def _get_blob_client(self, container_name: str, blob_name: str) -> BlobClient:
        """
        Gets a client for a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to interact with.

        Returns:
            BlobClient: A client to interact with a specific blob.
        """

        if not container_name or not blob_name:
            raise ValueError("Container name and blob name must be provided.")

        container_client = self._service_client.get_container_client(container_name)
        return container_client.get_blob_client(blob_name)

    async def delete_blob(self, container_name: str, blob_name: str) -> None:
        """
        Deletes a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to delete.

        Raises:
            ResourceNotFoundError: If the blob does not exist.
            HttpResponseError: If there is an issue with the Azure request.
        """

        blob_client = self._get_blob_client(container_name, blob_name)

        try:
            await blob_client.delete_blob()

        except ResourceNotFoundError as e:
            raise ResourceNotFoundError(
                f"Blob {blob_name} not found in container {container_name}."
            ) from e

        except HttpResponseError as e:
            raise HttpResponseError(
                f"Failed to delete blob {blob_name}: {e.message}"
            ) from e

    async def download_blob(self, container_name: str, blob_name: str) -> bytes:
        """
        Downloads the content of a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to download.

        Returns:
            bytes: The content of the blob.

        Raises:
            ResourceNotFoundError: If the blob does not exist.
            HttpResponseError: If there is an issue with the Azure request.
        """

        blob_client = self._get_blob_client(container_name, blob_name)

        try:
            blob_stream = await blob_client.download_blob()
            return await blob_stream.readall()

        except ResourceNotFoundError as e:
            raise ResourceNotFoundError(
                f"Blob {blob_name} not found in container {container_name}."
            ) from e

        except HttpResponseError as e:
            raise HttpResponseError(
                f"Failed to download blob {blob_name}: {e.message}"
            ) from e

    async def get_blob_metadata(
        self, container_name: str, blob_name: str
    ) -> Dict[str, Any]:
        """
        Retrieves the metadata of a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob.

        Returns:
            Dict[str, Any]: A dictionary containing the metadata key-value pairs of the blob.

        Raises:
            ResourceNotFoundError: If the blob does not exist.
        """
        try:
            blob_properties = await self.get_blob_properties(container_name, blob_name)
            return blob_properties.get("metadata", {})

        except ResourceNotFoundError as e:
            raise ResourceNotFoundError(
                f"Blob {blob_name} not found in container {container_name}."
            ) from e

    async def get_blob_properties(
        self, container_name: str, blob_name: str
    ) -> Dict[str, Any]:
        """
        Retrieves the properties and metadata of a specific blob.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to inspect.

        Returns:
            Dict[str, Any]: A dictionary containing blob properties.

        Raises:
            ResourceNotFoundError: If the blob does not exist.
            HttpResponseError: If there is an issue with the Azure request.
        """

        blob_client = self._get_blob_client(container_name, blob_name)

        try:
            properties = await blob_client.get_blob_properties()
            return {
                "metadata": properties.metadata,
                "size": properties.size,
                "last_modified": properties.last_modified,
                "content_type": properties.content_settings.content_type,
                "blob_type": properties.blob_type,
                "etag": properties.etag,
            }

        except ResourceNotFoundError as e:
            raise ResourceNotFoundError(
                f"Blob {blob_name} not found in container {container_name}."
            ) from e

        except HttpResponseError as e:
            raise HttpResponseError(
                f"Failed to get properties for blob {blob_name}: {e.message}"
            ) from e

    async def list_blobs(
        self,
        container_name: str,
        prefix: str = "",
        include_datasets: str | List[str] = None,
    ) -> AsyncIterable[BlobProperties]:
        """
        Lists blobs in a container with an optional prefix.

        Args:
            container_name (str): The name of the container.
            prefix (str, optional): The prefix to filter blobs. Defaults to "".

        Returns:
            AsyncIterable[BlobProperties]: An async iterable of blob properties.

        Raises:
            ResourceNotFoundError: If the container does not exist.
            HttpResponseError: If there is an issue with the Azure request.
        """

        if not container_name:
            raise ValueError("Container name must be provided.")

        container_client = self._service_client.get_container_client(container_name)

        try:
            async for blob in container_client.list_blobs(
                name_starts_with=prefix, include=include_datasets
            ):
                yield blob

        except ResourceNotFoundError as e:
            raise ResourceNotFoundError(f"Container {container_name} not found.") from e

        except HttpResponseError as e:
            raise HttpResponseError(
                f"Failed to list blobs in container {container_name}: {e.message}"
            ) from e

    async def upload_blob(
        self,
        container_name: str,
        blob_name: str,
        content: bytes,
        metadata: Optional[Dict[str, str]] = None,
        overwrite: bool = True,
    ) -> None:
        """
        Uploads a blob with the given content and metadata.

        Args:
            container_name (str): The name of the container.
            blob_name (str): The name of the blob to upload.
            content (bytes): The content of the blob.
            metadata (Dict[str, str], optional): The metadata to associate with the blob.

        Raises:
            HttpResponseError: If there is an issue with the Azure request.
        """

        blob_client = self._get_blob_client(container_name, blob_name)

        try:
            await blob_client.upload_blob(
                content, metadata=metadata or {}, overwrite=overwrite
            )

        except HttpResponseError as e:
            raise HttpResponseError(
                f"Failed to upload blob {blob_name}: {e.message}"
            ) from e
