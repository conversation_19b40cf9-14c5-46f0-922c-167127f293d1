"""
This module defines models for vector store operations.
"""

# Standard imports
from typing import List
from uuid import UUID

# External imports
from pydantic import BaseModel, Field

class DocumentProcessingError(BaseModel):
    """Model for document processing errors"""
    document_id: UUID
    error_message: str
    error_type: str

class ProjectDocumentDeleteResponse(BaseModel):
    """
    Model for the document deletion response.
    """

    id: UUID = Field(
        ...,
        description="An unique identifier for the deletion operation.",
        example="a86a6e3d-6516-4b88-9f92-e6e6277cc2b4",
    )
    project_id: UUID = Field(
        ...,
        description="The project ID from which the document was deleted.",
        example="550e8400-e29b-41d4-a716-************",
    )
    organization_id: UUID = Field(
        ...,
        description="The organization ID who owns the project.",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )
    document_id: UUID = Field(
        ...,
        description="The document ID that was deleted.",
        example="693b3787-37b5-4f82-8302-3ef96e2e6c1a",
    )
    message: str = Field(
        ...,
        description="Confirmation message for the document deletion.",
        example="Document deleted successfully.",
    )


class ProjectDocumentRequest(BaseModel):
    """
    Model for the document ingestion or remove request.
    """

    file_name: str = Field(
        ...,
        description="The file name to be ingested or removed.",
        example="document.pdf",
    )

class IngestDocumentRequest(BaseModel):
    """
    Model for the document ingestion or remove request.
    """
    id: UUID = Field(
        ...,
        description="The document id.",
        example="29f854c3-7816-438a-93ee-e39f66ecb3ed",
    )
    file_name: str = Field(
        ...,
        description="The file name to be ingested or removed.",
        example="document.pdf",
    )

class ProjectIngestResponse(BaseModel):
    """
    Model for the project ingestion response.
    """

    id: UUID = Field(
        ...,
        description="An unique identifier provided.",
        example="29f854c3-7816-438a-93ee-e39f66ecb3ed",
    )
    project_id: UUID = Field(
        ...,
        description="The project ID.",
        example="550e8400-e29b-41d4-a716-************",
    )
    organization_id: UUID = Field(
        ...,
        description="The organization ID.",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )
    blobs_processed: int = Field(
        ...,
        description="The number of blobs processed.",
        example=5,
    )
    documents_ingested: int = Field(
        ...,
        description="The number of documents ingested.",
        example=20,
    )
    successfully_processed: List[UUID] = Field(
        ...,
        description="List of successfully processed document IDs.",
        example=[
            "693b3787-37b5-4f82-8302-3ef96e2e6c1a",
            "d2a0e1f3-8b4d-4f8c-a5b9-7f3c5c3a0a9f",
        ],
    )
    processing_errors: List[DocumentProcessingError] = Field(
        default_factory=list,
        description="List of processing errors encountered during ingestion.",
        example=[{"document_id": "uuid", "error_message": "Failed to process", "error_type": "ProcessingError"}],
    )

class ProjectVectorStoreCreateResponse(BaseModel):
    """
    Model for the project vector store deletion response.
    """

    id: UUID = Field(
        ...,
        description="An unique identifier provided.",
        example="6a6e9584-e07e-46ca-ba17-a061cb51d8a8",
    )
    project_id: UUID = Field(
        ...,
        description="The project ID.",
        example="df3cfa77-ac7c-46a5-8b40-3af0dcb5613e",
    )
    organization_id: UUID = Field(
        ...,
        description="The organization ID.",
        example="fc07b825-eaa5-4815-b798-9a7a2cf1436b",
    )
    message: str = Field(
        ...,
        description="Confirmation message for the creation.",
        example="Vector store created successfully.",
    )


class ProjectVectorStoreDeleteResponse(BaseModel):
    """
    Model for the project vector store deletion response.
    """

    id: UUID = Field(
        ...,
        description="An unique identifier provided.",
        example="29f854c3-7816-438a-93ee-e39f66ecb3ed",
    )
    project_id: UUID = Field(
        ...,
        description="The project ID.",
        example="550e8400-e29b-41d4-a716-************",
    )
    organization_id: UUID = Field(
        ...,
        description="The organization ID.",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )
    message: str = Field(
        ...,
        description="Confirmation message for the deletion.",
        example="Vector store deleted successfully.",
    )

class ProjectVectorStoreExistsResponse(BaseModel):
    """Response model for checking if a vector store exists."""
    id: UUID = Field(
        ...,
        description="An unique identifier provided.",
        example="29f854c3-7816-438a-93ee-e39f66ecb3ed",
    )
    project_id: UUID = Field(
        ...,
        description="The project ID.",
        example="550e8400-e29b-41d4-a716-************",
    )
    organization_id: UUID = Field(
        ...,
        description="The organization ID.",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )
    exists: bool = Field(
        ...,
        description="True if vector store exists, False if it doesn\'t.",
        example="True"
    )
    message: str = Field(
        ...,
        description="Confirmation message for the deletion.",
        example="Vector store deleted successfully.",
    )

class DocumentToDelete(BaseModel):
    """Model for a document to be deleted."""
    document_id: UUID = Field(
        ...,
        description="The document ID to be deleted.",
        example="693b3787-37b5-4f82-8302-3ef96e2e6c1a",
    )
    file_name: str = Field(
        ...,
        description="The file name to be deleted.",
        example="document.pdf",
    )

class ProjectDocumentsDeleteRequest(BaseModel):
    """Model for batch document deletion request."""
    documents: List[DocumentToDelete] = Field(
        ...,
        description="List of documents to delete.",
    )

class DeleteResult(BaseModel):
    """Model for individual document deletion result."""
    document_id: UUID
    file_name: str
    success: bool
    message: str

class ProjectDocumentsDeleteResponse(BaseModel):
    """Model for batch document deletion response."""
    id: UUID = Field(
        ...,
        description="An unique identifier for the deletion operation.",
    )
    project_id: UUID = Field(..., description="The project ID.")
    organization_id: UUID = Field(..., description="The organization ID.")
    successful_deletions: List[DeleteResult] = Field(
        default_factory=list,
        description="List of successfully deleted documents.",
    )
    failed_deletions: List[DeleteResult] = Field(
        default_factory=list,
        description="List of documents that failed to delete.",
    )
