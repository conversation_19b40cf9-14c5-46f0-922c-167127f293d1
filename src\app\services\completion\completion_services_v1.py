"""
This module defines services for completion operations.
"""

# Standard imports
import logging
import os
import uuid
from abc import ABC, abstractmethod
from typing import Optional, AsyncGenerator

# External imports
from langchain_core.messages import BaseMessage
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureChatOpenAI
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler

# Internal imports
from src.app.models.chat.chat_models_v1 import StreamingChunk
from src.app.models.projects.completion_models_v1 import (
    CompletionOptions,
    CompletionType,
)

from src.app.prompts.documents.document_prompts_v1 import (
    DOCUMENT_DESCRIPTION_PROMPT,
    DOCUMENT_QUESTIONS_PROMPT,
    DOCUMENT_STRONG_POINTS_PROMPT,
    DOCUMENT_SUMMARY_PROMPT,
    MULTIPLE_DOCUMENTS_SUMMARY_PROMPT,
    DOCUMENT_WEAK_POINTS_PROMPT,
    DOCUMENT_TIMELINE_PROMPT,
)

from src.app.prompts.projects.project_prompts_v1 import (
    ADMINISTRATIVE_LAW_PROJECT_TIMELINE_PROMPT,
    FOLLOW_UP_QUESTIONS_PROMPT,
    GOVERNMENT_JUDGMENT_SUMMARY_PROMPT,
    OBJECTION_ARGUMENTS_PROMPT,
    OBJECTION_DECISION_DOCUMENT_PROMPT,
    OBJECTION_DECISION_PROMPT,
    PROJECT_JUDGMENT_PROMPT,
    PROJECT_QUESTIONS_PROMPT,
    PROJECT_STRONG_POINTS_PROMPT,
    PROJECT_SUMMARY_PROMPT,
    PROJECT_TIMELINE_PROMPT,
    PROJECT_WEAK_POINTS_PROMPT,
    STATEMENT_OF_DEFENCE_COUNTERCLAIM_CONTRADICTIONS_PROMPT,
    STATEMENT_OF_DEFENCE_COUNTERCLAIM_SUMMARY_PROMPT,
    STATEMENT_OF_DEFENCE_DEFENCE_AGAINST_CLAIM_PROMPT,
    STATEMENT_OF_DEFENCE_FACTUAL_DESCRIPTION_PARTY_POSITIONS_PROMPT,
    STATEMENT_OF_DEFENCE_LEGAL_GROUNDS_FOR_DEFENCE_AND_COUNTERCLAIMS_PROMPT,
    STATEMENT_OF_DEFENCE_COUNTERCLAIMS_PROMPT,
    STATEMENT_OF_DEFENCE_EVIDENCE_SUBMISSION_PROMPT,
    STATEMENT_OF_DEFENCE_INTRODUCTION_PROMPT,
    STATEMENT_OF_DEFENCE_CONCLUSION_AND_REQUESTS_TO_COURT_PROMPT,
    STATEMENT_OF_DEFENCE_PROMPT,
    STATEMENT_OF_DEFENCE_STRONG_POINTS_PROMPT,
    STATEMENT_OF_DEFENCE_WEAK_POINTS_PROMPT,
    WRIT_OF_SUMMONS_CONTRADICTIONS_PROMPT,
    WRIT_OF_SUMMONS_QUESTIONS_PROMPT,
    WRIT_OF_SUMMONS_SUMMARY_PROMPT,
    WRIT_OF_SUMMONS_TIMELINE_PROMPT,
)

from src.app.prompts.system.system_prompts_v1 import COMPLETION_SERVICES_SYSTEM_PROMPT
from src.app.services.retrieval.retrieval_services_v1 import RetrieverService


class CompletionService(ABC):
    """
    Base class for completion services.
    """

    NO_CONTEXT_FOUND_EN = "I do not know the answer based on the information provided."
    NO_CONTEXT_FOUND_NL = (
        "Ik weet het antwoord niet op basis van de beschikbare informatie."
    )

    def __init__(self, retriever_service: RetrieverService):
        """
        Initializes the completion service with environment variables.
        """
        if not retriever_service:
            raise ValueError("RetrieverService cannot be None")

        self._retriever_service = retriever_service
        self._init_variables()
        self._init_clients()

    def _init_variables(self):
        """
        Initializes environment variables.
        """

        self._openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self._openai_api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        self._openai_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        self._openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self._openai_model_name = os.getenv("AZURE_OPENAI_MODEL_NAME")
        self._openai_language = os.getenv("AZURE_OPENAI_LANGUAGE", "nl")
        self._openai_temperature = float(os.getenv("AZURE_OPENAI_TEMPERATURE", "0.70"))
        self._openai_top_p = float(os.getenv("AZURE_OPENAI_TOP_P", "0.85"))

    def _init_clients(self):
        """
        Initializes the Azure OpenAI clients.
        """

        try:
            self._chat_client = AzureChatOpenAI(
                azure_deployment=self._openai_deployment,
                model=self._openai_model_name,
                temperature=self._openai_temperature,
                api_version=self._openai_api_version,
                top_p=self._openai_top_p,
            )

            self._chat_stream_client = AzureChatOpenAI(
                azure_deployment=self._openai_deployment,
                model=self._openai_model_name,
                temperature=self._openai_temperature,
                api_version=self._openai_api_version,
                streaming=True,
                callbacks=[StreamingStdOutCallbackHandler()],
                top_p=self._openai_top_p,
                max_retries=3,
                model_kwargs={"stream_options": {"include_usage": True}},
            )

            logging.info("Info: Azure OpenAI chat model integration initialized.")

        except Exception as e:
            logging.error("Error initializing Azure OpenAI client: %s", e)
            raise

    @abstractmethod
    def get_completion(self, input_text: str, completion_options: CompletionOptions):
        """
        Gets chat completions.
        """

    @abstractmethod
    def get_completion_stream(
        self, input_text, completion_options: CompletionOptions, top=5
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Gets chat completions stream.
        """

    @abstractmethod
    def get_embeddings(self, text: str):
        """
        Get embeddings.
        """

    @abstractmethod
    def get_prompt(self, context: str, completion_options: CompletionOptions) -> list[BaseMessage]:
        """
        Returns a sample prompt based on prompt type.
        """

    def get_num_tokens(self, text: str) -> int:
        """
        Returns the number of tokens in the given document text.
        """
        tokens = self._chat_client.get_num_tokens(text)
        return tokens


class ProjectCompletionService(CompletionService):
    """
    Service for project completion operations.
    """

    PROJECT_COMPLETION_PROMPTS = {
        CompletionType.JUDGMENT: PROJECT_JUDGMENT_PROMPT,
        CompletionType.PROJECT_SUMMARY: PROJECT_SUMMARY_PROMPT,
        CompletionType.QUESTIONS: PROJECT_QUESTIONS_PROMPT,
        CompletionType.STATEMENT_OF_DEFENCE: STATEMENT_OF_DEFENCE_PROMPT,
        CompletionType.STATEMENT_OF_DEFENCE_FACTUAL_DESCRIPTION_PARTY_POSITIONS: STATEMENT_OF_DEFENCE_FACTUAL_DESCRIPTION_PARTY_POSITIONS_PROMPT,  # Temp
        CompletionType.STATEMENT_OF_DEFENCE_DEFENCE_AGAINST_CLAIM: STATEMENT_OF_DEFENCE_DEFENCE_AGAINST_CLAIM_PROMPT,  # Temp
        CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIMS: STATEMENT_OF_DEFENCE_COUNTERCLAIMS_PROMPT,  # Temp
        CompletionType.STATEMENT_OF_DEFENCE_INTRODUCTION: STATEMENT_OF_DEFENCE_INTRODUCTION_PROMPT,  # Temp
        CompletionType.STATEMENT_OF_DEFENCE_LEGAL_GROUNDS_FOR_DEFENCE_AND_COUNTERCLAIMS: STATEMENT_OF_DEFENCE_LEGAL_GROUNDS_FOR_DEFENCE_AND_COUNTERCLAIMS_PROMPT,  # Temp
        CompletionType.STATEMENT_OF_DEFENCE_EVIDENCE_SUBMISSION: STATEMENT_OF_DEFENCE_EVIDENCE_SUBMISSION_PROMPT,  # Temp
        CompletionType.STATEMENT_OF_DEFENCE_CONCLUSION_AND_REQUESTS_TO_COURT: STATEMENT_OF_DEFENCE_CONCLUSION_AND_REQUESTS_TO_COURT_PROMPT,  # Temp
        CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIM_CONTRADICTIONS: STATEMENT_OF_DEFENCE_COUNTERCLAIM_CONTRADICTIONS_PROMPT,
        CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIM_SUMMARY: STATEMENT_OF_DEFENCE_COUNTERCLAIM_SUMMARY_PROMPT,
        CompletionType.STATEMENT_OF_DEFENCE_STRONG_POINTS: STATEMENT_OF_DEFENCE_STRONG_POINTS_PROMPT,
        CompletionType.STATEMENT_OF_DEFENCE_WEAK_POINTS: STATEMENT_OF_DEFENCE_WEAK_POINTS_PROMPT,
        CompletionType.STRONG_POINTS: PROJECT_STRONG_POINTS_PROMPT,
        CompletionType.TIMELINE: PROJECT_TIMELINE_PROMPT,
        CompletionType.WEAK_POINTS: PROJECT_WEAK_POINTS_PROMPT,
        CompletionType.WRIT_OF_SUMMONS_CONTRADICTIONS: WRIT_OF_SUMMONS_CONTRADICTIONS_PROMPT,
        CompletionType.WRIT_OF_SUMMONS_QUESTIONS: WRIT_OF_SUMMONS_QUESTIONS_PROMPT,
        CompletionType.FOLLOW_UP_QUESTIONS: FOLLOW_UP_QUESTIONS_PROMPT,
        CompletionType.WRIT_OF_SUMMONS_SUMMARY: WRIT_OF_SUMMONS_SUMMARY_PROMPT,
        CompletionType.WRIT_OF_SUMMONS_TIMELINE: WRIT_OF_SUMMONS_TIMELINE_PROMPT,
        CompletionType.ADMINISTRATIVE_LAW_TIMELINE: ADMINISTRATIVE_LAW_PROJECT_TIMELINE_PROMPT,
        CompletionType.GOVERNMENT_JUDGMENT_SUMMARY: GOVERNMENT_JUDGMENT_SUMMARY_PROMPT,
        CompletionType.OBJECTION_ARGUMENTS: OBJECTION_ARGUMENTS_PROMPT,
        CompletionType.OBJECTION_DECISION: OBJECTION_DECISION_PROMPT,
        CompletionType.OBJECTION_DECISION_DOCUMENT: OBJECTION_DECISION_DOCUMENT_PROMPT,
    }

    async def get_completion(
        self,
        input_text: str,
        completion_options: CompletionOptions,
        top: Optional[int] = None,
    ) -> BaseMessage:
        """
        Performs a search using the retriever service and gets completion for a project prompt.

        Args:
            input_text (str): The input text for the completion.
            completion_options (CompletionOptions): The type of completion to generate.
            top (int, optional): The number of top search results to consider for context.

        Returns:
            BaseMessage: The completion result containing the generated content.

        Raises:
            Exception: If an error occurs during the RAG process, an exception is raised.
        """
        try:
            # Perform a search using the retriever service
            results = self._retriever_service.search_project(
                query=input_text, top_k=top
            )

            # Check if results are empty before compiling context
            if not results:
                logging.info("Info: No context found, skipping LLM invocation.")
                content = (
                    self.NO_CONTEXT_FOUND_NL
                    if self._openai_language == "nl"
                    else self.NO_CONTEXT_FOUND_EN
                )

                return BaseMessage(
                    id=f"run-{uuid.uuid4()}-0",
                    type="system",
                    content=content,
                    response_metadata={
                        "token_usage": {
                            "prompt_tokens": 0,
                            "completion_tokens": 0,
                            "total_tokens": 0,
                        }
                    },
                )

            # Compile the context from the search results
            context = "\n".join([doc.page_content for doc in results])
            prompts = self.get_prompt(context, completion_options)

            # Invoke the chat client to get the completion
            logging.info(f"Info: Generating {completion_options.completion_type} content from Azure OpenAI.")
            completion = self._chat_client.invoke(prompts)
            return completion

        except Exception as e:
            logging.error(
                "Error performing completion for project: %s", e
            )  # TODO: Issue 25 | Log an error should include detailed information about the exception
            raise

    async def get_completion_stream(
        self, input_text, completion_options: CompletionOptions, top=5
    ) -> AsyncGenerator[StreamingChunk, None]:
        try:
            # Perform a search using the retriever service
            results = self._retriever_service.search_project(input_text, top)

            # Check if results are empty before compiling context
            if not results:
                logging.info("No context found, skipping LLM invocation.")
                yield StreamingChunk(
                    content=(
                        self.NO_CONTEXT_FOUND_NL
                        if self._openai_language == "nl"
                        else self.NO_CONTEXT_FOUND_EN
                    ),
                    is_final=False,
                )
                # Send final chunk with metadata

                yield StreamingChunk(
                    content="",
                    is_final=True,
                    metadata={
                        "id": f"run-{uuid.uuid4()}-0",
                        "organization_id": str(self._retriever_service.organization_id),
                        "project_id": (
                            str(self._retriever_service.project_id)
                            if self._retriever_service.project_id
                            else None
                        ),
                        "prompt_tokens": 0,
                        "completion_tokens": 0,
                        "total_tokens": 0,
                    },
                )
                return

            # Compile the context from the search results
            context = "\n".join([doc.page_content for doc in results])
            prompts = self.get_prompt(context, completion_options)
            response_content = []
            # Invoke the chat client to get the completion
            logging.info(f"Info: Generating {completion_options.completion_type} content from Azure OpenAI.")
            final_chunk = None
            async for chunk in self._chat_stream_client.astream(prompts):
                if chunk.usage_metadata:
                    final_chunk = chunk
                    break
                content = chunk.content
                response_content.append(content)

                yield StreamingChunk(content=content, is_final=False)

            # Send final chunk with metadata
            metadata = {
                "id": f"run-{uuid.uuid4()}-0",
                "organization_id": str(self._retriever_service.organization_id),
                "project_id": (
                    str(self._retriever_service.project_id)
                    if self._retriever_service.project_id
                    else None
                ),
            }

            # If we received usage metadata, include it in the final chunk
            if final_chunk and final_chunk.usage_metadata:
                metadata.update({
                    "prompt_tokens": final_chunk.usage_metadata["input_tokens"],
                    "completion_tokens": final_chunk.usage_metadata["output_tokens"],
                    "total_tokens": final_chunk.usage_metadata["total_tokens"],
                })
            else:
                # If no usage metadata was received, provide default values
                logging.warning("No usage metadata received from OpenAI API")
                metadata.update({
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0,
                })

            yield StreamingChunk(
                content="",
                is_final=True,
                metadata=metadata,
            )

        except ValueError as e:
            logging.error("Validation error: %s", e)
            raise e

        except Exception as e:
            logging.error("Error getting project streaming response: %s", e)
            raise e

    def get_embeddings(self, text: str):
        """
        Get embeddings for a project text.
        """

        return self._retriever_service.embed_query(text)

    def get_prompt(
        self,
        context: str,
        completion_options: CompletionOptions,
    ) -> list[BaseMessage]:
        """
        Returns a template prompt based on the prompt type, with optional parameters filled in if provided.

        Args:
            context (str): The context that will be inserted into the prompt.
            completion_type (CompletionType): The type of completion, which determines the format of the prompt.
            completion_options (Optional[CompletionOptions]): Optional completion options containing parameters to be
            used in the prompt.

        Returns:
            list[BaseMessage]: List containing the system prompt and the  formatted user prompt with the context and
            parameters inserted.
        """

        completion_type = completion_options.completion_type
        if completion_type not in self.PROJECT_COMPLETION_PROMPTS:
            raise ValueError(f"Invalid prompt type: {completion_type}")

        prompt_template = self.PROJECT_COMPLETION_PROMPTS[completion_type]

        if completion_options and completion_options.params:
            # Format the prompt with the available context and parameters from completion_options
            prompt_with_context = prompt_template.format(
                context=context, **completion_options.params
            )
        else:
            # If no params or empty params, just use context
            prompt_with_context = prompt_template.format(context=context)

        return ChatPromptTemplate.from_messages([
            SystemMessage(content=COMPLETION_SERVICES_SYSTEM_PROMPT),
            HumanMessage(content=prompt_with_context)
        ]).format_messages()


class DocumentCompletionService(CompletionService):
    """
    Service for document completion operations.
    """

    DOCUMENT_COMPLETION_PROMPTS = {
        CompletionType.DESCRIPTION: DOCUMENT_DESCRIPTION_PROMPT,
        CompletionType.DOCUMENT_SUMMARY: MULTIPLE_DOCUMENTS_SUMMARY_PROMPT,
        CompletionType.QUESTIONS: DOCUMENT_QUESTIONS_PROMPT,
        CompletionType.STRONG_POINTS: DOCUMENT_STRONG_POINTS_PROMPT,
        CompletionType.WEAK_POINTS: DOCUMENT_WEAK_POINTS_PROMPT,
        CompletionType.TIMELINE: DOCUMENT_TIMELINE_PROMPT,
    }

    async def get_completion(
        self,
        input_text: str,
        completion_type: CompletionType,
        top: Optional[int] = None,
    ) -> BaseMessage:
        """
        Performs a search using the retriever service and gets completion for a document prompt.

        Args:
            input_text (str): The input text for the completion.
            completion_type (CompletionType): The type of completion to generate.
            top (int, optional): The number of top search results to consider for context.

        Returns:
            BaseMessage: The completion result containing the generated content.

        Raises:
            Exception: If an error occurs during the RAG process, an exception is raised.
        """
        try:
            # Perform a search using the retriever service
            results = self._retriever_service.search_project(input_text, top)

            # Check if results are empty before compiling context
            if not results:
                logging.info("No context found, skipping LLM invocation.")
                content = (
                    self.NO_CONTEXT_FOUND_NL
                    if self._openai_language == "nl"
                    else self.NO_CONTEXT_FOUND_EN
                )

                return BaseMessage(
                    id=f"run-{uuid.uuid4()}-0",
                    type="system",
                    content=content,
                    response_metadata={
                        "token_usage": {
                            "prompt_tokens": 0,
                            "completion_tokens": 0,
                            "total_tokens": 0,
                        }
                    },
                )

            # Compile the context from the search results
            # TODO: change this logic, this is currently dumping in the unordered context from the RAG
            context = "\n".join([doc.page_content for doc in results])
            prompts = self.get_prompt(context, completion_type)

            # Invoke the chat client to get the completion
            logging.info(f"Info: Generating {completion_type} content from Azure OpenAI.")
            completion = self._chat_client.invoke(prompts)
            return completion

        except Exception as e:
            logging.error("Error performing completion for document: %s", e)
            raise

    async def get_completion_stream(
        self, input_text, completion_options: CompletionOptions, top=5
    ) -> AsyncGenerator[StreamingChunk, None]:
        try:
            # Perform a search using the retriever service
            results = self._retriever_service.search_project(input_text, top)

            # Check if results are empty before compiling context
            if not results:
                logging.info("No context found, skipping LLM invocation.")
                yield StreamingChunk(
                    content=(
                        self.NO_CONTEXT_FOUND_NL
                        if self._openai_language == "nl"
                        else self.NO_CONTEXT_FOUND_EN
                    ),
                    is_final=False,
                )
                # Send final chunk with metadata

                yield StreamingChunk(
                    content="",
                    is_final=True,
                    metadata={
                        "id": None,
                        "organization_id": str(self._retriever_service.organization_id),
                        "project_id": (
                            str(self._retriever_service.project_id)
                            if self._retriever_service.project_id
                            else None
                        ),
                        "prompt_tokens": 0,
                        "completion_tokens": 0,
                        "total_tokens": 0,
                    },
                )
                return

            # Compile the context from the search results
            context = "\n".join([doc.page_content for doc in results])
            prompts = self.get_prompt(context, completion_options.completion_type)
            response_content = []
            # Invoke the chat client to get the completion
            logging.info(f"Info: Generating {completion_options.completion_type} content from Azure OpenAI.")
            final_chunk = None
            async for chunk in self._chat_stream_client.astream(prompts):
                if chunk.usage_metadata:
                    final_chunk = chunk
                    break
                content = chunk.content
                response_content.append(content)

                yield StreamingChunk(content=content, is_final=False)

            # Send final chunk with metadata
            metadata = {
                "id": f"run-{uuid.uuid4()}-0",
                "organization_id": str(self._retriever_service.organization_id),
                "project_id": (
                    str(self._retriever_service.project_id)
                    if self._retriever_service.project_id
                    else None
                ),
            }

            # If we received usage metadata, include it in the final chunk
            if final_chunk and final_chunk.usage_metadata:
                metadata.update({
                    "prompt_tokens": final_chunk.usage_metadata["input_tokens"],
                    "completion_tokens": final_chunk.usage_metadata["output_tokens"],
                    "total_tokens": final_chunk.usage_metadata["total_tokens"],
                })
            else:
                # If no usage metadata was received, provide default values
                logging.warning("No usage metadata received from OpenAI API")
                metadata.update({
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0,
                })

            yield StreamingChunk(
                content="",
                is_final=True,
                metadata=metadata,
            )

        except ValueError as e:
            logging.error("Validation error: %s", e)
            raise e

        except Exception as e:
            logging.error("Error getting document streaming response: %s", e)
            raise e

    def get_embeddings(self, text: str):
        """
        Get embeddings for a project text.
        """

        return self._retriever_service.embed_query(text)

    def get_prompt(self, context: str, completion_type: CompletionType) -> list[BaseMessage]:
        """
        Returns a template prompt based on the prompt type.
        """

        if completion_type not in self.DOCUMENT_COMPLETION_PROMPTS:
            raise ValueError(f"Invalid prompt type: {completion_type}")

        prompt_with_context = self.DOCUMENT_COMPLETION_PROMPTS[completion_type].format(context=context)

        return ChatPromptTemplate.from_messages([
            SystemMessage(content=COMPLETION_SERVICES_SYSTEM_PROMPT),
            HumanMessage(content=prompt_with_context)
        ]).format_messages()
