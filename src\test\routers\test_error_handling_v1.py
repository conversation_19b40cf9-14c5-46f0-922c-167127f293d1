import pytest
from fastapi import HTTPException
from fastapi.responses import JSONResponse
from azure.core.exceptions import ResourceNotFoundError, HttpResponseError
from uuid import UUID
from unittest.mock import AsyncMock, patch

from src.app.routers.documents.document_router_v1 import _handle_error as doc_handle_error
from src.app.routers.projects.project_router_v1 import _handle_error as proj_handle_error
from src.app.routers.retrieval.vector_store_router_v1 import _handle_error as vec_handle_error

class TestErrorHandling:
    @pytest.fixture
    def organization_id(self):
        return UUID('550e8400-e29b-41d4-a716-************')

    @pytest.fixture
    def project_id(self):
        return UUID('550e8400-e29b-41d4-a716-************')

    @pytest.fixture
    def document_ids(self):
        return [UUID('550e8400-e29b-41d4-a716-************')]

    def test_document_router_resource_not_found(self, organization_id, project_id, document_ids):
        """Test handling of ResourceNotFoundError in document router"""
        error = ResourceNotFoundError("Document not found")
        response = doc_handle_error(error, organization_id, project_id, document_ids, "test action")
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 404
        assert "ResourceNotFoundError" in response.body.decode()
        assert "Document not found" in response.body.decode()

    def test_project_router_resource_not_found(self, organization_id, project_id):
        """Test handling of ResourceNotFoundError in project router"""
        error = ResourceNotFoundError("Project not found")
        response = proj_handle_error(error, organization_id, project_id, "test action")
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 404
        assert "ResourceNotFoundError" in response.body.decode()
        assert "Project not found" in response.body.decode()

    def test_vector_store_router_http_response_error(self, organization_id, project_id):
        """Test handling of HttpResponseError in vector store router"""
        error = HttpResponseError("Too many vector stores")
        error.status_code = 429
        response = vec_handle_error(error, organization_id, project_id, "test action")
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 507
        assert "TooManyVectorStores" in response.body.decode()

    def test_generic_error_handling(self, organization_id, project_id, document_ids):
        """Test handling of generic exceptions"""
        error = Exception("Generic error")
        
        # Test document router
        doc_response = doc_handle_error(error, organization_id, project_id, document_ids, "test action")
        assert doc_response.status_code == 500
        assert "InternalServerError" in doc_response.body.decode()
        
        # Test project router
        proj_response = proj_handle_error(error, organization_id, project_id, "test action")
        assert proj_response.status_code == 500
        assert "InternalServerError" in proj_response.body.decode()
        
        # Test vector store router
        vec_response = vec_handle_error(error, organization_id, project_id, "test action")
        assert vec_response.status_code == 500
        assert "InternalServerError" in vec_response.body.decode()

    def test_error_logging(self, organization_id, project_id, document_ids, caplog):
        """Test error logging across all routers"""
        error = Exception("Test error")
        
        # Test document router logging
        doc_handle_error(error, organization_id, project_id, document_ids, "test action")
        assert "Error generating test action for document" in caplog.text
        
        # Test project router logging
        proj_handle_error(error, organization_id, project_id, "test action")
        assert "Error generating test action for organization" in caplog.text
        
        # Test vector store router logging
        vec_handle_error(error, organization_id, project_id, "test action")
        assert "Vector store action test action failed" in caplog.text
