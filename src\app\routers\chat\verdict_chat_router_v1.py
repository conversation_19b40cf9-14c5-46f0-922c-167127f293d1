"""
API router for verdict chat integration endpoints.
Combines rechtspraak search with chat functionality.
"""

from typing import Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, Path, Query, Request, HTTPException, status
from fastapi.responses import JSONResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import SpanKind
from pydantic import BaseModel, Field

from src.app.configurations.telemetry.telemetry_configs_v1 import (
    get_logger,
    get_open_telemetry_tracer,
)
from src.app.services.verdict_chat_service import VerdictChatService


api_logger = get_logger(__name__)
api_tracer = get_open_telemetry_tracer()
router = APIRouter(prefix="/{organization_id}/verdict-chat", tags=["Verdict Chat"])


class StartVerdictChatRequest(BaseModel):
    """Request model for starting a chat with a specific verdict."""
    
    user_id: str = Field(..., description="The user ID")
    session_id: str = Field(..., description="The chat session ID")
    verdict_id: str = Field(..., description="The ECLI verdict ID", example="ECLI:NL:HR:2023:123")
    fetch_full_content: bool = Field(True, description="Whether to fetch full verdict content")


class SearchAndChatRequest(BaseModel):
    """Request model for searching verdicts and starting a chat."""
    
    user_id: str = Field(..., description="The user ID")
    session_id: str = Field(..., description="The chat session ID")
    search_query: str = Field(..., min_length=3, description="The search query")
    legal_areas: Optional[List[str]] = Field(None, description="Legal area filters")
    sub_areas: Optional[List[str]] = Field(None, description="Legal sub-area filters")
    auto_select_first: bool = Field(False, description="Auto-start chat with first result")


class VerdictChatResponse(BaseModel):
    """Response model for verdict chat operations."""
    
    message_id: str = Field(..., description="The seeded message ID")
    verdict_id: str = Field(..., description="The verdict ID")
    session_id: str = Field(..., description="The chat session ID")
    metadata: dict = Field(..., description="Verdict metadata")
    content_length: int = Field(..., description="Length of verdict content")
    summary: Optional[str] = Field(None, description="Verdict summary")


class SearchAndChatResponse(BaseModel):
    """Response model for search and chat operations."""
    
    search_query: str = Field(..., description="The search query used")
    verdict_ids: List[str] = Field(..., description="Found verdict IDs")
    count: int = Field(..., description="Number of verdicts found")
    chat_initialized: Optional[VerdictChatResponse] = Field(None, description="Chat initialization result")


@router.post(
    "/start",
    response_model=VerdictChatResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Start chat with specific verdict",
    description="Initialize a chat conversation with a specific verdict by ECLI ID"
)
async def start_verdict_chat(
    request: Request,
    organization_id: UUID = Path(..., description="Organization ID"),
    chat_request: StartVerdictChatRequest = ...,
):
    """
    Start a chat conversation with a specific verdict.
    
    This endpoint:
    1. Fetches the verdict content from rechtspraak.nl (or cache)
    2. Seeds the chat conversation with the verdict
    3. Returns the initialized chat information
    """
    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            verdict_chat_service = VerdictChatService()
            
            result = await verdict_chat_service.start_chat_with_verdict(
                organization_id=organization_id,
                user_id=chat_request.user_id,
                session_id=chat_request.session_id,
                verdict_id=chat_request.verdict_id,
                fetch_full_content=chat_request.fetch_full_content
            )
            
            # Add summary for user display
            summary = verdict_chat_service.get_verdict_summary(
                verdict_id=result["verdict_id"],
                metadata=result["metadata"]
            )
            result["summary"] = summary
            
            return VerdictChatResponse(**result)

    except ValueError as e:
        # Handle validation errors (like duplicate verdicts)
        api_logger.warning(f"Validation error starting verdict chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(f"Error starting verdict chat: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting chat with verdict {chat_request.verdict_id}"
        )


@router.post(
    "/search-and-start",
    response_model=SearchAndChatResponse,
    status_code=status.HTTP_200_OK,
    summary="Search verdicts and optionally start chat",
    description="Search for verdicts and optionally initialize chat with the first result"
)
async def search_and_start_chat(
    request: Request,
    organization_id: UUID = Path(..., description="Organization ID"),
    search_request: SearchAndChatRequest = ...,
):
    """
    Search for verdicts and optionally start a chat with the first result.
    
    This endpoint:
    1. Searches for verdicts using the provided query and filters
    2. Optionally initializes a chat with the first result
    3. Returns search results and chat initialization status
    """
    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            verdict_chat_service = VerdictChatService()
            
            result = await verdict_chat_service.search_and_start_chat(
                organization_id=organization_id,
                user_id=search_request.user_id,
                session_id=search_request.session_id,
                search_query=search_request.search_query,
                legal_areas=search_request.legal_areas,
                sub_areas=search_request.sub_areas,
                auto_select_first=search_request.auto_select_first
            )
            
            return SearchAndChatResponse(**result)

    except Exception as e:
        api_logger.error(f"Error in search and start chat: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error searching verdicts and starting chat"
        )


@router.get(
    "/sessions/{session_id}/verdicts",
    response_model=List[dict],
    status_code=status.HTTP_200_OK,
    summary="List seeded verdicts in session",
    description="Get all verdicts that have been seeded in a chat session"
)
async def list_session_verdicts(
    request: Request,
    organization_id: UUID = Path(..., description="Organization ID"),
    session_id: str = Path(..., description="Chat session ID"),
    user_id: str = Query(..., description="User ID"),
):
    """
    List all verdicts that have been seeded in a specific chat session.
    
    This is useful for:
    - Showing users which verdicts are available in the conversation
    - Debugging chat context
    - Building conversation summaries
    """
    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            verdict_chat_service = VerdictChatService()
            
            verdicts = await verdict_chat_service.list_seeded_verdicts(
                organization_id=organization_id,
                user_id=user_id,
                session_id=session_id
            )
            
            return verdicts

    except Exception as e:
        api_logger.error(f"Error listing session verdicts: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing verdicts for session {session_id}"
        )


@router.get(
    "/verdicts/{verdict_id}/summary",
    response_model=dict,
    status_code=status.HTTP_200_OK,
    summary="Get verdict summary",
    description="Get a formatted summary of a specific verdict"
)
async def get_verdict_summary(
    request: Request,
    organization_id: UUID = Path(..., description="Organization ID"),
    verdict_id: str = Path(..., description="ECLI verdict ID"),
):
    """
    Get a formatted summary of a specific verdict.
    
    This endpoint can be used to preview verdict information
    before starting a chat conversation.
    """
    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            verdict_chat_service = VerdictChatService()
            
            # Fetch verdict metadata (without full content for performance)
            _, metadata = await verdict_chat_service._fetch_verdict_content(verdict_id)
            
            summary = verdict_chat_service.get_verdict_summary(verdict_id, metadata)
            
            return {
                "verdict_id": verdict_id,
                "summary": summary,
                "metadata": metadata
            }

    except Exception as e:
        api_logger.error(f"Error getting verdict summary: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting summary for verdict {verdict_id}"
        )
