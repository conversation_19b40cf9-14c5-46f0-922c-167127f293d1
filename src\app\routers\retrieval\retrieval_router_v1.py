"""
Module for handling project-related search and retrieval in the system.
"""

# Standard imports
from uuid import UUID

# External imports
from fastapi import APIRouter, HTTPException, Request, status, Path, Query
from fastapi.responses import JSONResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import SpanKind

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v1 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.services.retrieval.retrieval_services_v1 import RetrieverService


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


@api_router.get(
    "/{organization_id}/projects/{project_id}/retrieve",
    description="Retrieves content within a specific project based on the query.",
    operation_id="retrieve_project_content",
    response_description="The retrieved project",
    summary="Retrieves content for a project. ☑️",
)
async def retrieve_project(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
    query: str = Query("*", description="The search query to retrieve the content."),
    top: int = Query(5, description="The number of results to return."),
) -> JSONResponse:
    """
    Retrieves a project based on the query.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        query (str): The search query to retrieve the content.
        top (int): The number of results to return.

    Returns:
        JSONResponse: A response containing the retrieved content for the project.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):

            retriever_service = RetrieverService(
                organization_id=organization_id,
                project_id=project_id if project_id else None,
            )

            search_results = retriever_service.search_project(query, top)
            search_content = "\n".join(
                [result.page_content for result in search_results]
            )

            return JSONResponse(
                {"data": search_content}, status_code=status.HTTP_200_OK
            )

    except Exception as e:
        api_logger.error("Error retrieving project %s", project_id, exc_info=True)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving content from project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/search",
    description="Searches for content within a specific project based on the query.",
    operation_id="search_project_content",
    response_description="The search results for projects",
    summary="Searches within a project. ☑️",
)
async def search_project(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
    query: str = Query("*", description="The search query."),
    top: int = Query(5, description="The number of results to return."),
) -> JSONResponse:
    """
    Searches for content within a specific project based on the query.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        query (str): The search query.
        top (int): The number of results to return.

    Returns:
        JSONResponse: A response containing the search results for the project.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):

            retriever_service = RetrieverService(
                organization_id=organization_id,
                project_id=project_id if project_id else None,
            )

            search_results = retriever_service.search_project(query, top)
            search_content = "\n".join(
                [result.page_content for result in search_results]
            )

            return JSONResponse(
                {"data": search_content}, status_code=status.HTTP_200_OK
            )

    except Exception as e:
        api_logger.error("Error searching for project %s", project_id, exc_info=True)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching content in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/vector_search",
    description="Searches for content within a specific project based on the vector query.",
    operation_id="vector_search_project_content",
    response_description="The search results for projects",
    summary="Searches within a project using vector query. ☑️",
)
async def vector_search_project(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
    query: str = Query("*", description="The search query (vector-based)."),
    top: int = Query(5, description="The number of results to return."),
) -> JSONResponse:
    """
    Searches for content within a specific project based on the vector query.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        query (str): The search query (vector-based).
        top (int): The number of results to return.

    Returns:
        JSONResponse: A response containing the vector search results for the project.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):

            retriever_service = RetrieverService(
                organization_id=organization_id,
                project_id=project_id if project_id else None,
            )
            search_results = retriever_service.search_project(query, top)
            search_content = "\n".join(
                [result.page_content for result in search_results]
            )

            return JSONResponse(
                {"data": search_content}, status_code=status.HTTP_200_OK
            )

    except Exception as e:
        api_logger.error(
            "Error performing vector search for project %s", project_id, exc_info=True
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error performing vector search in project {project_id}.",
        ) from e
