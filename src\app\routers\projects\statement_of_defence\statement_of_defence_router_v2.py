"""
This module defines FastAPI routers for project operations.
"""

# Standard imports
import json
from typing import Optional, AsyncGenerator
from uuid import UUID

# External imports
from fastapi import APIR<PERSON>er, Header, HTTPException, Path, Query, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, StreamingResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import get_current_span, SpanKind, Status, StatusCode
from starlette.responses import StreamingResponse, JSONResponse

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v2 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.models.documents.document_models_v2 import DocumentType
from src.app.models.projects.completion_models_v2 import (
    CompletionOptions,
    CompletionType,
    ProjectCompletionResponse,
)

from src.app.services.completion.completion_services_v2 import ProjectCompletionService
from src.app.services.retrieval.retrieval_services_v2 import RetrieverService


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


async def stream_generator(
    completion_service: ProjectCompletionService,
    input_text: str,
    completion_options: CompletionOptions,
    top: Optional[int] = None,
) -> AsyncGenerator[str, None]:
    """
    Generates a stream of chat responses.
    Each chunk is formatted as JSON for easy parsing by the C# client.
    Args:
        completion_service (ProjectCompletionService): The completion service to use.
        input_text (str): The input text to use for completion.
        completion_options (CompletionType): The type of completion to generate.
        top (Optional[int], optional): The number of completions to generate. Defaults to None.
    Yields:
        StreamingChunk: A chunk of the AI response as it is generated
    Raises:
        HTTPException: If an error occurs during the streaming process.
    """

    try:
        async for chunk in completion_service.get_completion_stream(
            input_text, completion_options=completion_options
        ):
            # Convert chunk to JSON that C# can easily parse
            chunk_dict = chunk.model_dump()
            yield f"{json.dumps(chunk_dict)}\n"

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        ) from e


async def generate_response(
    organization_id: UUID,
    project_id: UUID,
    completion_options: CompletionOptions,
    document_type: Optional[DocumentType] = None,
    input_text: str = "*",
    use_streaming: bool = False,
) -> StreamingResponse | JSONResponse:
    """
    Generates a response based on the specified prompt type for a given project.

    This function retrieves the necessary documents for the project, generates a completion
    using the specified prompt type, and returns the result as a JSON response.
    """

    try:
        retriever_service = RetrieverService(
            organization_id=organization_id,
            project_id=project_id if project_id else None,
            document_type=document_type.name if document_type else None,
        )

        completion_service = ProjectCompletionService(retriever_service)
        if use_streaming:
            return StreamingResponse(
                stream_generator(
                    completion_service,
                    input_text=input_text,
                    completion_options=completion_options,
                ),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream",
                    "X-Accel-Buffering": "no",
                },
            )
        completion_results = await completion_service.get_completion(
            input_text=input_text,
            completion_options=completion_options,
        )

        # The field will be in the response model with the generated content
        content_field = get_content_field(completion_options.completion_type)
        response_data = ProjectCompletionResponse(
            id=completion_results.id,
            project_id=project_id,
            organization_id=organization_id,
            **{content_field: completion_results.content},
            prompt_tokens=completion_results.response_metadata["token_usage"][
                "prompt_tokens"
            ],
            completion_tokens=completion_results.response_metadata["token_usage"][
                "completion_tokens"
            ],
            total_tokens=completion_results.response_metadata["token_usage"][
                "total_tokens"
            ],
        )

        return JSONResponse(
            content=jsonable_encoder(response_data),
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        api_logger.error(
            "Error generating response for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        current_span = get_current_span()
        if current_span:
            current_span.set_status(Status(StatusCode.ERROR, str(e)))

        # Raise so the caller (route) can handle it
        raise e


def get_content_field(completion_type: CompletionType) -> str:
    """
    Maps a completion type to the corresponding field in the project response model.

    Args:
        completion_type (CompletionType): The type of completion to be mapped.

    Returns:
        str: The name of the field in the response model where the generated content
        will be placed.
    """

    content_field_mapping = {
        CompletionType.STATEMENT_OF_DEFENCE: "statement_of_defence",
        CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIMS: "statement_of_defence_counterclaims",
        CompletionType.STATEMENT_OF_DEFENCE_CONCLUSION_AND_REQUESTS_TO_COURT: "statement_of_defence_conclusion_and_requests_to_court",
        CompletionType.STATEMENT_OF_DEFENCE_DEFENCE_AGAINST_CLAIM: "statement_of_defence_defence_against_claim",
        CompletionType.STATEMENT_OF_DEFENCE_EVIDENCE_SUBMISSION: "statement_of_defence_evidence_submission",
        CompletionType.STATEMENT_OF_DEFENCE_FACTUAL_DESCRIPTION_PARTY_POSITIONS: "statement_of_defence_factual_description_party_positions",
        CompletionType.STATEMENT_OF_DEFENCE_INTRODUCTION: "statement_of_defence_introduction",
        CompletionType.STATEMENT_OF_DEFENCE_LEGAL_GROUNDS_FOR_DEFENCE_AND_COUNTERCLAIMS: "statement_of_defence_legal_grounds_for_defence_and_counterclaims",
        CompletionType.STATEMENT_OF_DEFENCE_STRONG_POINTS: "statement_of_defence_strengths",
        CompletionType.STATEMENT_OF_DEFENCE_WEAK_POINTS: "statement_of_defence_weaknesses",
    }

    return content_field_mapping[completion_type]


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence_v2",
    description="Generates the statement of defence for a project, based on the provided documents.",
    operation_id="generate_statement_of_defence_v2",
    response_description="The generated statement of defence for the project.",
    response_model=ProjectCompletionResponse,
    summary="Statement of defence for a project. ⚠️",
)
async def generate_statement_of_defence_v2(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the statement of defence for a project based on the provided documents.
    This version aggregates the factual description, positions of the parties,
    defence against the claim, counterclaims, legal grounds for defence and counterclaims,
    evidence submission, and conclusion into a single response.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: A response containing the generated statement of defence for the project.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            # "Factual Description and Positions of the Parties"
            introduction_response = await generate_response(
                organization_id,
                project_id,
                completion_options=CompletionOptions(
                    CompletionType.STATEMENT_OF_DEFENCE_INTRODUCTION,
                    params={
                        "claimant": claimant,
                        "defendant": defendant,
                    },
                ),
                use_streaming=use_streaming,
            )

            introduction_data = json.loads(introduction_response.body.decode("utf-8"))

            # "Factual Description and Positions of the Parties"
            factual_description_response = await generate_response(
                organization_id,
                project_id,
                completion_options=CompletionOptions(
                    CompletionType.STATEMENT_OF_DEFENCE_FACTUAL_DESCRIPTION_PARTY_POSITIONS,
                    params={
                        "claimant": claimant,
                        "defendant": defendant,
                    },
                ),
                use_streaming=use_streaming,
            )

            factual_description_data = json.loads(
                factual_description_response.body.decode("utf-8")
            )

            # "Defence Against the Claim" section
            defence_against_claim_response = await generate_response(
                organization_id,
                project_id,
                completion_options=CompletionOptions(
                    CompletionType.STATEMENT_OF_DEFENCE_DEFENCE_AGAINST_CLAIM,
                    params={
                        "claimant": claimant,
                        "defendant": defendant,
                    },
                ),
                use_streaming=use_streaming,
            )

            defence_against_claim_data = json.loads(
                defence_against_claim_response.body.decode("utf-8")
            )

            # "Counterclaims" section
            counterclaims_response = await generate_response(
                organization_id,
                project_id,
                completion_options=CompletionOptions(
                    CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIMS,
                    params={
                        "claimant": claimant,
                        "defendant": defendant,
                    },
                ),
                use_streaming=use_streaming,
            )

            counterclaims_data = json.loads(counterclaims_response.body.decode("utf-8"))

            # "Legal Grounds for Defence and Counterclaims" section
            legal_grounds_response = await generate_response(
                organization_id,
                project_id,
                completion_options=CompletionOptions(
                    CompletionType.STATEMENT_OF_DEFENCE_LEGAL_GROUNDS_FOR_DEFENCE_AND_COUNTERCLAIMS,
                    params={
                        "claimant": claimant,
                        "defendant": defendant,
                    },
                ),
                use_streaming=use_streaming,
            )

            legal_grounds_data = json.loads(legal_grounds_response.body.decode("utf-8"))

            # "Evidence Submission" section
            evidence_submission_response = await generate_response(
                organization_id,
                project_id,
                completion_options=CompletionOptions(
                    CompletionType.STATEMENT_OF_DEFENCE_EVIDENCE_SUBMISSION,
                    params={
                        "claimant": claimant,
                        "defendant": defendant,
                    },
                ),
                use_streaming=use_streaming,
            )

            evidence_submission_data = json.loads(
                evidence_submission_response.body.decode("utf-8")
            )

            # "Conclusion and Requests to Court" section
            conclusion_response = await generate_response(
                organization_id,
                project_id,
                completion_options=CompletionOptions(
                    CompletionType.STATEMENT_OF_DEFENCE_CONCLUSION_AND_REQUESTS_TO_COURT,
                    params={
                        "claimant": claimant,
                        "defendant": defendant,
                    },
                ),
                use_streaming=use_streaming,
            )

            conclusion_data = json.loads(conclusion_response.body.decode("utf-8"))

            # All generated sections into the final Statement of Defence content
            statement_of_defence_content = "\n\n".join(
                [
                    introduction_data.get("statement_of_defence_introduction", ""),
                    factual_description_data.get(
                        "statement_of_defence_factual_description_party_positions", ""
                    ),
                    defence_against_claim_data.get(
                        "statement_of_defence_defence_against_claim", ""
                    ),
                    counterclaims_data.get("statement_of_defence_counterclaims", ""),
                    legal_grounds_data.get(
                        "statement_of_defence_legal_grounds_for_defence_and_counterclaims",
                        "",
                    ),
                    evidence_submission_data.get(
                        "statement_of_defence_evidence_submission", ""
                    ),
                    conclusion_data.get(
                        "statement_of_defence_conclusion_and_requests_to_court", ""
                    ),
                ]
            )

            response_data = ProjectCompletionResponse(
                id=introduction_data.get("id"),  # Uses first response's ID (for now).
                project_id=project_id,
                organization_id=organization_id,
                statement_of_defence=statement_of_defence_content,
                statement_of_defence_introduction=introduction_data.get(
                    "statement_of_defence_introduction", ""
                ),
                statement_of_defence_factual_description_party_positions=factual_description_data.get(
                    "statement_of_defence_factual_description_party_positions", ""
                ),
                statement_of_defence_defence_against_claim=defence_against_claim_data.get(
                    "statement_of_defence_defence_against_claim", ""
                ),
                statement_of_defence_counterclaims=counterclaims_data.get(
                    "statement_of_defence_counterclaims", ""
                ),
                statement_of_defence_legal_grounds_for_defence_and_counterclaims=legal_grounds_data.get(
                    "statement_of_defence_legal_grounds_for_defence_and_counterclaims",
                    "",
                ),
                statement_of_defence_evidence_submission=evidence_submission_data.get(
                    "statement_of_defence_evidence_submission", ""
                ),
                statement_of_defence_conclusion_and_requests_to_court=conclusion_data.get(
                    "statement_of_defence_conclusion_and_requests_to_court", ""
                ),
                prompt_tokens=sum(
                    [
                        introduction_data.get("prompt_tokens", 0),
                        factual_description_data.get("prompt_tokens", 0),
                        defence_against_claim_data.get("prompt_tokens", 0),
                        counterclaims_data.get("prompt_tokens", 0),
                        legal_grounds_data.get("prompt_tokens", 0),
                        evidence_submission_data.get("prompt_tokens", 0),
                        conclusion_data.get("prompt_tokens", 0),
                    ]
                ),
                completion_tokens=sum(
                    [
                        introduction_data.get("completion_tokens", 0),
                        factual_description_data.get("completion_tokens", 0),
                        defence_against_claim_data.get("completion_tokens", 0),
                        counterclaims_data.get("completion_tokens", 0),
                        legal_grounds_data.get("completion_tokens", 0),
                        evidence_submission_data.get("completion_tokens", 0),
                        conclusion_data.get("completion_tokens", 0),
                    ]
                ),
                total_tokens=sum(
                    [
                        introduction_data.get("total_tokens", 0),
                        factual_description_data.get("total_tokens", 0),
                        defence_against_claim_data.get("total_tokens", 0),
                        counterclaims_data.get("total_tokens", 0),
                        legal_grounds_data.get("total_tokens", 0),
                        evidence_submission_data.get("total_tokens", 0),
                        conclusion_data.get("total_tokens", 0),
                    ]
                ),
            )

            return JSONResponse(
                content=jsonable_encoder(response_data),
                status_code=status.HTTP_200_OK,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence v2 for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence v2 for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/strong_points",
    description="Generates strong points based on the statement of defence for a project.",
    operation_id="generate_statement_of_defence_strong_points",
    response_description="The generated strong points for the project's statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Strong points from the statement of defence. ⚠️",
)
async def generate_statement_of_defence_strong_points(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates strong points based on the statement of defence and other related documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated strong points for the project's statement of defence.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_STRONG_POINTS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                document_type=DocumentType.STATEMENT_OF_DEFENCE,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence strong points for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence strong points for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/weak_points",
    description="Generates weak points based on the statement of defence for a project.",
    operation_id="generate_statement_of_defence_weak_points",
    response_description="The generated weak points for the project's statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Weak points from the statement of defence. ⚠️",
)
async def generate_statement_of_defence_weak_points(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates weak points based on the statement of defence and other related documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated weak points for the project's statement of defence.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_WEAK_POINTS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                document_type=DocumentType.STATEMENT_OF_DEFENCE,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence weak points for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence weak points for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/factual_description_party_positions",
    description="Generates the 'Factual Description and Positions of the Parties' section for the Statement of Defence.",
    operation_id="generate_statement_of_defence_factual_description_party_positions",
    response_description="The factual description and positions of the parties for the statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Factual description and positions of the parties for the statement of defence. ⚠️",
)
async def generate_statement_of_defence_factual_description_party_positions(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the 'Factual Description and Positions of the Parties' section of the Statement of Defence.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated content for the section.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_FACTUAL_DESCRIPTION_PARTY_POSITIONS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options=completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence's factual description and positions of the parties for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence's factual description and positions of the parties for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/defence_against_claim",
    description="Generates the 'Defence Against the Claim' section for the statement of defence.",
    operation_id="generate_statement_of_defence_defence_against_claim",
    response_description="The defence against the claim for the statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Defence against the claim for the statement of defence. ⚠️",
)
async def generate_statement_of_defence_defence_against_claim(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the 'Defence Against the Claim' section of the Statement of Defence.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated content for the section.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_DEFENCE_AGAINST_CLAIM,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options=completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence's defence against claim for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence's defence against the claim for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/counterclaims",
    description="Generates the 'Counterclaims' section for the statement of defence.",
    operation_id="generate_statement_of_defence_counterclaims",
    response_description="The counterclaims section for the statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Counterclaims for the statement of defence. ⚠️",
)
async def generate_statement_of_defence_counterclaims(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the 'Counterclaims' section of the Statement of Defence.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated content for the section.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIMS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options=completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence counterclaims for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence counterclaims for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/legal_grounds_for_defence_and_counterclaims",
    description="Generates the 'Legal Grounds for Defence and Counterclaims' section for the statement of defence.",
    operation_id="generate_statement_of_defence_legal_grounds_for_defence_and_counterclaims",
    response_description="The legal grounds for defence and counterclaims section for the statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Legal grounds for defence and counterclaims for the statement of defence. ⚠️",
)
async def generate_statement_of_defence_legal_grounds_for_defence_and_counterclaims(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the 'Legal Grounds for Defence and Counterclaims' section of the Statement of Defence.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated content for the section.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_LEGAL_GROUNDS_FOR_DEFENCE_AND_COUNTERCLAIMS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options=completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence legal grounds for defence and counterclaims for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence legal grounds for defence and counterclaims for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/evidence_submission",
    description="Generates the 'Evidence Submission' section for the Statement of Defence.",
    operation_id="generate_statement_of_defence_evidence_submission",
    response_description="The evidence submission section for the statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Evidence submission for the statement of defence. ⚠️",
)
async def generate_statement_of_defence_evidence_submission(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the 'Evidence Submission' section of the Statement of Defence.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated content for the section.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_EVIDENCE_SUBMISSION,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options=completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence evidence submission for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence evidence submission for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/conclusion_and_requests_to_court",
    description="Generates the 'Conclusion and Requests to the Court' section for the statement of defence.",
    operation_id="generate_statement_of_defence_conclusion_and_requests_to_court",
    response_description="The conclusion and requests to the court section for the statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Conclusion and requests to the court for the statement of defence. ⚠️",
)
async def generate_statement_of_defence_conclusion_and_requests_to_court(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the 'Conclusion and Requests to the Court' section of the Statement of Defence.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated content for the section.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_CONCLUSION_AND_REQUESTS_TO_COURT,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options=completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence conclusion and requests to court for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence conclusion and requests to court for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence/introduction",
    description="Generates the 'Introduction' section for the statement of defence.",
    operation_id="generate_statement_of_defence_introduction",
    response_description="The introduction section for the statement of defence.",
    response_model=ProjectCompletionResponse,
    summary="Introduction for the statement of defence. ⚠️",
)
async def generate_statement_of_defence_introduction(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the 'Introduction' section of the Statement of Defence.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated content for the introduction section.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_INTRODUCTION,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options=completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating statement of defence introduction for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating statement of defence introduction for organization {organization_id} in project {project_id}.",
        ) from e
