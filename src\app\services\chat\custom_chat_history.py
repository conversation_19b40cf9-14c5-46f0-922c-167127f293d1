"""
This module defines a custom chat message history service that extends the functionality of LangChain's CosmosDBChatMessageHistory.
"""

from __future__ import annotations

import logging
from typing import List

from langchain_core.messages import BaseMessage

from langchain_community.chat_message_histories.cosmos_db import (
    CosmosDBChatMessageHistory,
)

DEFAULT_SESSION_ID = "default_session_id"
DEFAULT_USER_ID = "default_user_id"


class CustomCosmosDBChatMessageHistory(CosmosDBChatMessageHistory):
    """
    Custom chat message history service that extends CosmosDBChatMessageHistory
    to support a `requires_response` flag.
    """

    def __init__(
        self,
        cosmos_endpoint: str,
        cosmos_database: str,
        cosmos_container: str,
        connection_string: str,
        session_id: str = DEFAULT_SESSION_ID,
        user_id: str = DEFAULT_USER_ID,
        ttl: int | None = None,
    ):
        super().__init__(
            cosmos_endpoint=cosmos_endpoint,
            cosmos_database=cosmos_database,
            cosmos_container=cosmos_container,
            connection_string=connection_string,
            session_id=session_id,
            user_id=user_id,
            ttl=ttl,
        )

    def add_messages(
        self, messages: List[BaseMessage], requires_response: bool = True
    ) -> None:
        """
        Append new messages to the chat history in Cosmos DB.

        Args:
            messages: A list of BaseMessage objects to append.
            requires_response: A boolean flag indicating whether the messages require a response from the LLM.
        """
        # Add the requires_response flag to each message's additional_kwargs
        for message in messages:
            if not hasattr(message, 'additional_kwargs'):
                message.additional_kwargs = {}
            message.additional_kwargs['requires_response'] = requires_response

        # Use the parent class's add_messages method to leverage LangChain's built-in functionality
        try:
            super().add_messages(messages)
            logging.info(f"Successfully added {len(messages)} messages with requires_response={requires_response}")
        except Exception as e:
            logging.error(f"Failed to add messages to chat history: {e}", exc_info=True)
            raise

