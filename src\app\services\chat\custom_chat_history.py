"""
This module defines a custom chat message history service that extends the functionality of LangChain's CosmosDBChatMessageHistory.
"""

from __future__ import annotations

import json
import logging
from typing import List
import datetime

from langchain_core.messages import BaseMessage, message_to_dict

from langchain_community.chat_message_histories.cosmos_db import (
    CosmosDBChatMessageHistory,
)

# Define default values since they're not exported in newer versions
DEFAULT_SESSION_ID = "default_session"
DEFAULT_USER_ID = "default_user"


class CustomCosmosDBChatMessageHistory(CosmosDBChatMessageHistory):
    """
    Custom chat message history service that extends CosmosDBChatMessageHistory
    to support a `requires_response` flag.
    """

    def __init__(
        self,
        cosmos_endpoint: str,
        cosmos_database: str,
        cosmos_container: str,
        connection_string: str,
        session_id: str = DEFAULT_SESSION_ID,
        user_id: str = DEFAULT_USER_ID,
        ttl: int | None = None,
    ):
        super().__init__(
            cosmos_endpoint=cosmos_endpoint,
            cosmos_database=cosmos_database,
            cosmos_container=cosmos_container,
            connection_string=connection_string,
            session_id=session_id,
            user_id=user_id,
            ttl=ttl,
        )

    def add_messages(
        self, messages: List[BaseMessage], requires_response: bool = True
    ) -> None:
        """
        Append new messages to the chat history in Cosmos DB.

        Args:
            messages: A list of BaseMessage objects to append.
            requires_response: A boolean flag indicating whether the messages require a response from the LLM.
        """
        if self.cosmos_client is None:
            raise ValueError("Cosmos DB client is not initialized.")

        failed_messages = []
        for message in messages:
            document = self._create_document(message, requires_response)
            try:
                self.container_client.create_item(body=document)
            except Exception as e:
                error_msg = f"Error while saving message {message.id} to Cosmos DB: {e}"
                logging.error(error_msg)
                raise 
        
        if failed_messages:
            logging.warning(f"Failed to save {len(failed_messages)} messages: {failed_messages}")


    def _create_document(self, message: BaseMessage, requires_response: bool) -> dict:
        """
        Create a Cosmos DB document from a message, including the `requires_response` flag.

        Args:
            message: The BaseMessage object to convert.
            requires_response: The boolean flag to include in the document.

        Returns:
            A dictionary representing the Cosmos DB document.
        """
        try:
            message_json = json.dumps(message_to_dict(message))
        except (TypeError, ValueError) as e:
            raise ValueError(f"Failed to serialize message {message.id}: {e}")

        return {
            "id": message.id,
            "SessionId": self.session_id,
            "UserId": self.user_id,
            "Type": message.type,
            "Message": message_json,
            "requires_response": requires_response,
            "CreatedAt": datetime.datetime.now(datetime.timezone.utc).isoformat(),
        }

