"""
This module defines services for chat completion operations.
"""

# Standard imports
import logging
import os
import uuid
from typing import AsyncGenerator, Optional, List
from uuid import UUID

# External imports
from langchain_community.chat_message_histories.cosmos_db import (
    CosmosDBChatMessageHistory,
)

from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.documents import Document
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON><PERSON>plate, MessagesPlaceholder
from langchain_openai import AzureChatOpenAI

# Internal imports
from src.app.models.chat.chat_models_v2 import ChatRequest, ChatResponse, StreamingChunk
from src.app.models.context_models_v2 import Context
from src.app.prompts.chat.chat_prompts import (
    CHAT_INSTRUCTIONS_PROMPT,
    RAG_CHAT_INSTRUCTIONS_PROMPT,
    JURISPRUDENCE_CHAT_INSTRUCTIONS_PROMPT,
    RAG_JURISPRUDENCE_CHAT_INSTRUCTIONS_PROMPT,
)

from src.app.prompts.system.system_prompts_v2 import (
    RAG_CHAT_SYSTEM_PROMPT,
    RAG_JURISPRUDENCE_CHAT_SYSTEM_PROMPT,
)

from src.app.services.retrieval.retrieval_services_v2 import (
    RetrieverService,
    RetrieverSearchType,
)


class ChatHistoryService:
    """
    Service for managing chat history.
    """

    def __init__(self, organization_id: UUID, user_id: str, session_id: str) -> None:
        """
        Initializes the chat history service.

        Args:
            organization_id (UUID): The ID of the organization.
            user_id (str): The ID of the user.
            session_id (str): The ID of the session.
        """

        self.organization_id = organization_id
        self.user_id = user_id
        self.session_id = session_id

        self._validate_env_vars()

    def _validate_env_vars(self) -> None:
        """
        Validates the required environment variables for chat message history.

        Raises:
            ValueError: If one or more environment variables are not set.
        """

        required_vars = [
            os.getenv("AZURE_COSMOS_ENDPOINT"),
            os.getenv("AZURE_COSMOS_DATABASE_NAME"),
            os.getenv("AZURE_COSMOS_CONNECTION_STRING"),
        ]

        if not all(required_vars):
            raise ValueError(
                "One or more Azure Cosmos DB environment variables are not set."
            )

    def get_chat_history(self) -> BaseChatMessageHistory:
        """
        Creates and returns a chat history instance.

        Returns:
            BaseChatMessageHistory: An instance of CosmosDBChatMessageHistory.
        """

        try:
            chat_history = CosmosDBChatMessageHistory(
                cosmos_endpoint=os.getenv("AZURE_COSMOS_ENDPOINT"),
                cosmos_database=os.getenv("AZURE_COSMOS_DATABASE_NAME"),
                connection_string=os.getenv("AZURE_COSMOS_CONNECTION_STRING"),
                cosmos_container=f"org-{str(self.organization_id)}",
                user_id=self.user_id,
                session_id=self.session_id,
                ttl=int(os.getenv("AZURE_COSMOS_TTL", "604800")),
            )

            chat_history.prepare_cosmos()
            return chat_history

        except Exception as e:
            logging.error("Error preparing Azure Cosmos DB message history: %s", e)
            raise


class ChatService:
    """
    Service for handling chat operations.
    """

    def __init__(
        self,
        organization_id: UUID,
        project_id: Optional[UUID] = None,
        message_history: Optional[BaseChatMessageHistory] = None,
        retriever: Optional[RetrieverService] = None,
    ) -> None:
        """
        Initializes the chat service with the organization and project IDs and message history.

        Args:
            organization_id (UUID): The ID of the organization.
            project_id (Optional[UUID]): The ID of the project.
            message_history (Optional[BaseChatMessageHistory]): The message history handler.
            retriever (Optional[RetrieverService]): The retrieval service for context.
        """

        self._organization_id = organization_id
        self._message_history = message_history
        self._project_id = project_id
        self._retriever = retriever
        self._init_variables()
        self._init_clients()

    def _init_variables(self) -> None:
        """
        Initializes environment variables for Azure OpenAI.

        Raises:
            ValueError: If one or more environment variables are not set.
        """

        self._openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self._openai_api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        self._openai_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        self._openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self._openai_model_name = os.getenv("AZURE_OPENAI_MODEL_NAME")
        self._openai_temperature = float(
            os.getenv("AZURE_OPENAI_TEMPERATURE_CHAT", "0.5")
        )
        self._openai_top_p = float(os.getenv("AZURE_OPENAI_TOP_P_CHAT", "0.5"))
        self._search_top_k = int(os.getenv("AZURE_SEARCH_TOP_K_RESULTS_CHAT", "5"))

        self._validate_env_vars()

    def _validate_env_vars(self) -> None:
        """
        Validates the required environment variables for Azure OpenAI.

        Raises:
            ValueError: If one or more environment variables are not set.
        """

        required_vars = [
            self._openai_api_key,
            self._openai_api_version,
            self._openai_deployment,
            self._openai_endpoint,
            self._openai_model_name,
        ]

        if not all(required_vars):
            raise ValueError(
                "One or more Azure OpenAI environment variables are not set."
            )

    def _init_clients(self) -> None:
        """
        Initializes both streaming and non-streaming Azure OpenAI clients.
        Raises:
            Exception: If an error occurs during client initialization.
        """
        try:
            # Non-streaming client
            self._chat_client = AzureChatOpenAI(
                deployment_name=self._openai_deployment,
                model=self._openai_model_name,
                temperature=self._openai_temperature,
                api_version=self._openai_api_version,
                top_p=self._openai_top_p,
                streaming=False,
                max_retries=3,
            )

            # Streaming client
            self._streaming_client = AzureChatOpenAI(
                deployment_name=self._openai_deployment,
                model=self._openai_model_name,
                temperature=self._openai_temperature,
                api_version=self._openai_api_version,
                top_p=self._openai_top_p,
                streaming=True,
                max_retries=3,
                model_kwargs={"stream_options": {"include_usage": True}},
            )

            logging.info("Info: Azure OpenAI chat clients initialized.")

        except Exception as e:
            logging.error("Error initializing Azure OpenAI clients: %s", e)
            raise

    def _create_prompt(
        self, context: Optional[Context] = None, rechtspraak_search_performed=False
    ) -> ChatPromptTemplate:
        """
        Creates the chat prompt template.

        Args:
            context (Optional[str]): Context to guide the AI response.

        Returns:
            ChatPromptTemplate: The constructed prompt template for the chat.
        """

        if context.project_context:
            if rechtspraak_search_performed:
                instructions_prompt = RAG_JURISPRUDENCE_CHAT_INSTRUCTIONS_PROMPT.format(
                    context=context.project_context,
                    jurisprudence=(
                        context.jurisprudence_context
                        if context.jurisprudence_context
                        else "No cases have been found."
                    ),
                )
            else:
                instructions_prompt = RAG_CHAT_INSTRUCTIONS_PROMPT.format(
                    context=context.project_context
                )
        elif rechtspraak_search_performed:
            instructions_prompt = JURISPRUDENCE_CHAT_INSTRUCTIONS_PROMPT.format(
                jurisprudence=context.jurisprudence_context
            )
        else:
            instructions_prompt = CHAT_INSTRUCTIONS_PROMPT

        sys_message = (
            RAG_JURISPRUDENCE_CHAT_SYSTEM_PROMPT.format(
                instructions=instructions_prompt
            )
            if context.jurisprudence_context
            else RAG_CHAT_SYSTEM_PROMPT.format(instructions=instructions_prompt)
        )
        return ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=sys_message),
                MessagesPlaceholder("chat_history", optional=True),
                ("human", "{user_message}"),
            ]
        )

    def _validate_chat_request(self, chat_request: ChatRequest) -> None:
        """
        Validates the user's input message.

        Args:
            chat_request (ChatRequest): The chat request to validate.

        Raises:
            ValueError: If the user message is empty.
        """

        if not chat_request.user_message:
            raise ValueError("Input message cannot be empty.")

    def _retrieve_context(self, chat_request: ChatRequest) -> Context:
        """
        Retrieves context from both project and jurisprudence sources.

        Args:
            chat_request: ChatRequest containing the user's message

        Returns:
            Context: Combined context from both sources
        """
        project_context = ""

        # Get project context if available
        if self._retriever.project_id:
            project_docs = self._retriever.search_project(
                chat_request.user_message,
                self._search_top_k,
                RetrieverSearchType.VECTOR,
            )

            project_context = "\n".join([doc.page_content for doc in project_docs])

        # Get jurisprudence if enabled
        jurisprudence_results = []
        if self._retriever.include_jurisprudence:
            # Enhance query with project context if available
            enhanced_query = (
                f"{project_context}\n{chat_request.user_message}"
                if project_context
                else chat_request.user_message
            )

            # Get jurisprudence results as JSON
            jurisprudence_results = self._retriever.search_jurisprudence(
                query=enhanced_query, top_k=self._search_top_k
            )

            logging.info(f"Found {len(jurisprudence_results)} jurisprudence documents")

        return Context(
            project_context=project_context,
            jurisprudence_context=(
                jurisprudence_results if len(jurisprudence_results) else None
            ),
        )

    async def get_chat_response(self, chat_request: ChatRequest) -> ChatResponse:
        """Gets the chat response from Azure OpenAI."""
        try:
            self._validate_chat_request(chat_request)

            # Get context
            if chat_request.predefined_prompt:
                results = self._retriever.search_project(query="*", top_k=None)
                chat_context = Context(
                    project_context="\n".join([doc.page_content for doc in results]),
                    jurisprudence_context=None,
                )
            else:
                chat_context = (
                    self._retrieve_context(chat_request) if self._retriever else ""
                )

            chat_prompt = self._create_prompt(
                chat_context, rechtspraak_search_performed=chat_request.jurisprudence
            )
            chat_chain = chat_prompt | self._chat_client

            # Invoke the chain with history
            chat_history = (
                self._message_history.messages if self._message_history else []
            )
            chain_result = await chat_chain.ainvoke(
                {
                    "chat_history": chat_history,
                    "user_message": chat_request.user_message,
                }
            )

            # Add messages to history
            if self._message_history:
                self._message_history.add_messages(
                    [
                        HumanMessage(content=chat_request.user_message),
                        AIMessage(content=chain_result.content),
                    ]
                )

            return ChatResponse(
                id=chain_result.id,
                organization_id=self._organization_id,
                project_id=self._project_id,
                session_id=chat_request.session_id,
                user_id=chat_request.user_id,
                user_message=chat_request.user_message,
                content=chain_result.content,
                prompt_tokens=chain_result.response_metadata["token_usage"][
                    "prompt_tokens"
                ],
                completion_tokens=chain_result.response_metadata["token_usage"][
                    "completion_tokens"
                ],
                total_tokens=chain_result.response_metadata["token_usage"][
                    "total_tokens"
                ],
            )

        except ValueError as e:
            logging.error("Validation error: %s", e)
            raise e

        except Exception as e:
            logging.error("Error getting chat response: %s", e)
            raise e

    async def astream_chat_response(
        self, chat_request: ChatRequest
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Streams chat responses with token usage information.

        Args:
            chat_request (ChatRequest): The chat request containing the user message.

        Yields:
            StreamingChunk: Contains content chunk and metadata

        Raises:
            ValueError: If the user message is empty.
            Exception: If an error occurs during response generation.
        """
        try:
            self._validate_chat_request(chat_request)

            if chat_request.predefined_prompt:
                results = self._retriever.search_project(query="*", top_k=None)
                chat_context = Context(
                    project_context="\n".join([doc.page_content for doc in results]),
                    jurisprudence_context=None,
                )

            else:
                chat_context = (
                    self._retrieve_context(chat_request) if self._retriever else ""
                )

            chat_prompt = self._create_prompt(
                chat_context, rechtspraak_search_performed=chat_request.jurisprudence
            )

            # Create chain with streaming client
            chat_chain = chat_prompt | self._streaming_client
            response_content = []
            final_chunk = None
            # Stream the content
            async for chunk in chat_chain.astream(
                {
                    "chat_history": self._message_history.messages,
                    "user_message": chat_request.user_message,
                }
            ):
                if chunk.usage_metadata:
                    final_chunk = chunk
                    break
                content = chunk.content
                response_content.append(content)

                yield StreamingChunk(content=content, is_final=False)

            # Add messages to history
            self._message_history.add_messages(
                [
                    HumanMessage(content=chat_request.user_message),
                    AIMessage(content="".join(response_content)),
                ]
            )

            # Send final chunk with metadata
            yield StreamingChunk(
                content="",
                is_final=True,
                metadata={
                    "id": final_chunk.id if final_chunk else f"run-{uuid.uuid4()}-0",
                    "organization_id": str(self._organization_id),
                    "project_id": str(self._project_id) if self._project_id else None,
                    "session_id": chat_request.session_id,
                    "user_id": chat_request.user_id,
                    "user_message": chat_request.user_message,
                    "prompt_tokens": final_chunk.usage_metadata["input_tokens"],
                    "completion_tokens": final_chunk.usage_metadata["output_tokens"],
                    "total_tokens": final_chunk.usage_metadata["total_tokens"],
                },
            )

        except ValueError as e:
            logging.error("Validation error: %s", e)
            raise e

        except Exception as e:
            logging.error("Error getting chat response: %s", e)
            raise e
