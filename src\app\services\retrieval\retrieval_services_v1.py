"""
This module defines services for retrieval operations.
"""

# Standard imports
import logging
import os
from enum import Enum
from typing import List, Optional
from uuid import UUID

# External imports
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import ResourceNotFoundError
from azure.search.documents.indexes import SearchIndexClient
from azure.search.documents.indexes.models import (
    SearchField,
    SearchFieldDataType,
    SearchableField,
    SimpleField,
    ComplexField,
)

from langchain_core.documents import Document
from langchain_openai import AzureOpenAIEmbeddings
from langchain_community.vectorstores.azuresearch import AzureSearch


class RetrieverSearchType(str, Enum):
    """
    Retriever search type options.
    """

    SIMILARITY = "similarity"
    MMR = "mmr"
    SCORE_THRESHOLD = "similarity_score_threshold"
    VECTOR = "vector"


class RetrieverService:
    """
    Service for project retrieval operations.
    """

    def __init__(
        self,
        organization_id: UUID,
        include_jurisprudence: Optional[bool] = False,
        project_id: Optional[UUID] = None,
        document_ids: Optional[List[UUID]] = [],
        document_type: Optional[str] = None,
        legal_areas: Optional[List[str]] = None,
        legal_sub_areas: Optional[List[str]] = None,
    ) -> None:
        """
        Initializes the project retriever service.

        Args:
            organization_id (UUID): The organization ID.
            project_id (UUID): The project ID.
            document_type (Optional[str], optional): The document type. Defaults to None.
        """

        self.project_id = project_id
        self.include_jurisprudence = include_jurisprudence
        self.organization_id = organization_id
        self.document_ids = document_ids
        self.document_type = document_type
        self.legal_areas = legal_areas or []
        self.legal_sub_areas = legal_sub_areas or []
        self._init_variables()
        self._check_vector_store_exists()
        self._init_clients()

    def _init_variables(self) -> None:
        """
        Initializes environment variables.
        """

        self._container_prefix = os.getenv(
            "AZURE_STORAGE_ACCOUNT_CONTAINER_PREFIX", "org-"
        )
        self._datasource_prefix = os.getenv("AZURE_SEARCH_DATASOURCE_PREFIX", "dsc-")
        self._index_prefix = os.getenv("AZURE_SEARCH_INDEX_PREFIX", "idx-")
        self._indexer_prefix = os.getenv("AZURE_SEARCH_INDEXER_PREFIX", "idr-")
        self._openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self._openai_api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        self._openai_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        self._openai_embeddings_deployment = os.getenv(
            "AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT_NAME"
        )
        self._openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self._path_prefix = os.getenv("AZURE_STORAGE_ACCOUNT_PATH_PREFIX", "prj-")
        self._search_admin_key = os.getenv("AZURE_SEARCH_ADMIN_KEY")
        self._search_endpoint = os.getenv("AZURE_SEARCH_ENDPOINT")
        self._search_top_k_results = int(os.getenv("AZURE_SEARCH_TOP_K_RESULTS", "50"))
        self._storage_account_key = os.getenv("AZURE_STORAGE_ACCOUNT_KEY")
        self._storage_account_name = os.getenv("AZURE_STORAGE_ACCOUNT_NAME")

    def _init_clients(self) -> None:
        """
        Initializes the Azure OpenAI and Azure Search clients.
        """

        self._embeddings_client = AzureOpenAIEmbeddings(
            azure_deployment=self._openai_embeddings_deployment,
            api_key=self._openai_api_key,
            azure_endpoint=self._openai_endpoint,
            api_version=self._openai_api_version,
            chunk_size=1,
        )

        logging.info("Info: Azure OpenAI embeddings model integration initialized.")
        if self.project_id:
            self._project_search_client = AzureSearch(
                azure_search_endpoint=self._search_endpoint,
                azure_search_key=self._search_admin_key,
                index_name=f"{self._index_prefix}{self.project_id}",
                fields=self.get_project_fields(),
                embedding_function=self._embeddings_client.embed_query,
            )
            logging.info("Info: Azure OpenAI project search client initialized.")
        if self.include_jurisprudence:
            self._jurisprudence_search_client = AzureSearch(
                azure_search_endpoint=self._search_endpoint,
                azure_search_key=self._search_admin_key,
                index_name="idx-rechtspraak",
                fields=self.get_jurisprudence_fields(),
                embedding_function=self._embeddings_client.embed_query,
            )
            logging.info("Info: Azure OpenAI jurisprudence search client initialized.")

    def _check_vector_store_exists(self) -> None:
        """
        Checks if the vector store exists for the project.

        Raises:
            ResourceNotFoundError: If the vector store doesn't exist for the project.
        """
        if self.project_id:
            try:
                # Initialize Azure Search client to check if index exists
                client = SearchIndexClient(
                    self._search_endpoint, AzureKeyCredential(self._search_admin_key)
                )
                index_name = f"{self._index_prefix}{self.project_id}"
                index_names = [name for name in client.list_index_names()]
                if index_name not in index_names:
                    raise ResourceNotFoundError(
                        f"Vector store for project {self.project_id} not found."
                    )
            except Exception:
                raise ResourceNotFoundError(
                    f"Vector store for project {self.project_id} not found."
                )

    @staticmethod
    def get_project_fields() -> List[SearchField]:
        """
        Gets additional fields.

        Returns:
            List[SearchField]: A list of additional search fields.
        """

        return [
            SimpleField(
                name="id", type=SearchFieldDataType.String, key=True, filterable=True
            ),
            SearchableField(
                name="content", type=SearchFieldDataType.String, searchable=True
            ),
            SearchField(
                name="content_vector",
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name="myHnswProfile",
            ),
            SearchableField(
                name="metadata", type=SearchFieldDataType.String, searchable=True
            ),
            SearchableField(
                name="document_id",
                type=SearchFieldDataType.String,
                searchable=True,
                filterable=True,
            ),
            SearchableField(
                name="document_type",
                type=SearchFieldDataType.String,
                searchable=True,
                filterable=True,
            ),
            SimpleField(
                name="project_id", type=SearchFieldDataType.String, filterable=True
            ),
        ]

    @staticmethod
    def get_jurisprudence_fields() -> List[SearchField]:
        """
        Gets the field definitions for the jurisprudence index.

        Returns:
            List[SearchField]: A list of search fields for jurisprudence data.
        """

        return [
            SimpleField(
                name="id",
                type=SearchFieldDataType.String,
                filterable=True,
                retrievable=True,
            ),
            SimpleField(
                name="legal_area",
                type=SearchFieldDataType.String,
                filterable=True,
                searchable=True,
            ),
            SimpleField(
                name="legal_sub_area", type=SearchFieldDataType.String, filterable=True
            ),
            SearchableField(
                name="search_field", type=SearchFieldDataType.String, searchable=True
            ),
            SearchField(
                name="content_vector",
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name="myHnswConfig",
            ),
            ComplexField(
                name="case_content",
                fields=[
                    SimpleField(
                        name="full_text",
                        type=SearchFieldDataType.String,
                        filterable=True,
                        retrievable=True,
                    )
                ],
            ),
            ComplexField(
                name="case_metadata",
                fields=[
                    SimpleField(
                        name="title",
                        type=SearchFieldDataType.String,
                        filterable=True,
                        retrievable=True,
                    ),
                    SimpleField(
                        name="date",
                        type=SearchFieldDataType.DateTimeOffset,
                        filterable=True,
                        retrievable=True,
                    ),
                    SimpleField(
                        name="link",
                        type=SearchFieldDataType.String,
                        filterable=True,
                        retrievable=True,
                    ),
                    SimpleField(
                        name="summary",
                        type=SearchFieldDataType.String,
                        retrievable=True,
                    ),
                ],
            ),
        ]

    def embed_query(self, text: str) -> List[float]:
        """
        Embeds query text.

        Args:
            text (str): The text to embed.

        Returns:
            List[float]: The embedded query as a list of floats.
        """

        return self._embeddings_client.embed_query(text)

    def build_project_filters(self) -> str:
        """
        Builds specific filters for search or retrieval.

        Returns:
            str: A filter string for the search query.
        """

        filters = f"project_id eq '{self.project_id}'"
        if len(self.document_ids):
            document_id_list = "|".join(str(doc_id) for doc_id in self.document_ids)
            filters += f" and search.in(document_id, '{document_id_list}', '|')"
        if self.document_type:
            filters += f" and document_type eq '{self.document_type}'"
        return filters

    def build_jurisprudence_filters(self) -> str | None:
        """
        Builds specific filters for search or retrieval.

        Returns:
            str: A filter string for the search query.
        """

        filters = []
        if self.legal_areas:
            areas = "|".join(self.legal_areas)
            filters.append(f"search.in(legal_area, '{areas}', '|')")
        if self.legal_sub_areas:
            sub_areas = "|".join(self.legal_sub_areas)
            filters.append(f"search.in(legal_sub_area, '{sub_areas}', '|')")
        final_filter = " and ".join(filters) if filters else None
        return final_filter

    def search_project(
        self,
        query: str,
        top_k: Optional[int] = None,
        search_type: RetrieverSearchType = RetrieverSearchType.SIMILARITY,
    ) -> List[Document]:
        """
        Searches for documents based on the query.

        Args:
            query (str): The search query.
            top_k (Optional[int], optional): Number of top results.
            search_type (RetrieverSearchType): The type of search to perform.

        Returns:
            List[Document]: A list of matching documents.

        Raises:
            Exception: If an error occurs during the search.
        """

        logging.info("Info: Searching documents from Azure AI Search.")
        filters = self.build_project_filters()
        top_k = top_k or self._search_top_k_results

        try:
            if search_type == RetrieverSearchType.VECTOR:
                return self._project_search_client.vector_search(
                    query=query, k=top_k, filters=filters
                )
            else:
                return self._project_search_client.search(
                    query=query, search_type=str(search_type.value), filters=filters, k=top_k
                )
        except Exception as e:
            logging.error("Error during search: %s", e)
            raise

    def search_jurisprudence(
        self, query: str, top_k: Optional[int] = None
    ) -> List[dict]:
        """
        Searches jurisprudence documents based on query.

        Args:
            query (str): Search query.
            top_k (Optional[int]): Maximum number of results.

        Returns:
            List[dict]: List of documents in JSON format.
        """
        try:
            filters = self.build_jurisprudence_filters()
            top_k = top_k or self._search_top_k_results

            raw_results = self._jurisprudence_search_client.client.search(
                search_text=query,
                include_total_count=True,
                search_fields=["search_field"],
                select="id, legal_area, legal_sub_area, case_content, case_metadata",
                filter=filters,
                top=top_k,
            )

            results = []
            for result in raw_results:
                try:
                    results.append(dict(result))
                except Exception as e:
                    logging.error(
                        "Error processing search result: %s", e, exc_info=True
                    )
                    continue
            return results
        except Exception as e:
            logging.error("Error in search_jurisprudence: %s", e, exc_info=True)
            raise

    def retrieve_project(
        self, query: str, top_k: Optional[int] = None
    ) -> List[Document]:
        """
        Retrieves project documents using vector similarity.

        Args:
            query (str): The query to use for retrieval.
            top_k (Optional[int]): Number of top documents to retrieve.

        Returns:
            List[Document]: Retrieved documents.

        Raises:
            Exception: If retrieval fails.
        """

        logging.info("Info: Retrieving documents from Azure AI Search.")
        top_k = top_k or self._search_top_k_results

        try:
            results = self._project_search_client.search(
                query=query, search_type="similarity", k=top_k
            )
            return results
        except Exception as e:
            logging.error("Error during retrieval: %s", e)
            raise

    async def retrieve_jurisprudence(
        self, query: str, top_k: Optional[int] = None
    ) -> List[Document]:
        """
        Asynchronously retrieves jurisprudence documents using vector similarity.

        Args:
            query (str): The query to use for retrieval.
            top_k (Optional[int]): Number of top documents to retrieve.

        Returns:
            List[Document]: Retrieved jurisprudence documents.

        Raises:
            Exception: If retrieval fails.
        """

        logging.info("Info: Retrieving documents from Azure AI Search.")
        top_k = top_k or self._search_top_k_results

        try:
            results = self._jurisprudence_search_client.search(
                query=query, search_type="similarity", k=top_k
            )
            return results
        except Exception as e:
            logging.error(
                "Error during retrieval: %s", e, exc_info=True
            )  # TODO: Issue 25 | Log an error should include detailed information about the exception
            raise
