"""
Module to configure logging, tracing (OpenTelemetry), and Azure Monitor for telemetry.
"""

# Standard imports
import logging
import os
from logging import Logger

# External imports
from azure.monitor.opentelemetry import configure_azure_monitor
from opentelemetry.trace import get_tracer, get_tracer_provider, Tracer, TracerProvider


def configure_telemetry(logger: Logger) -> Tracer:
    """
    Configures logging, tracing (OpenTelemetry), and Azure Monitor telemetry.

    This function integrates logging, tracing, and telemetry configuration into a single method
    and returns a tracer instance.

    Args:
        logger (Logger): The logger instance to be configured.

    Returns:
        Tracer: The configured OpenTelemetry tracer instance.
    """

    # Configure logging internally
    _setup_logging()
    _setup_open_telemetry(logger)

    tracer = get_open_telemetry_tracer()
    return tracer


def get_open_telemetry_tracer(
    trace_provider: TracerProvider = get_tracer_provider(),
) -> Tracer:
    """
    Configures tracing (OpenTelemetry) for the application.

    This function sets up OpenTelemetry tracing with a tracer provider, which is used
    to monitor the application's distributed tracing and performance.

    Returns:
        Tracer: The configured OpenTelemetry tracer instance.
    """

    tracer = get_tracer(__name__, tracer_provider=trace_provider)
    return tracer


def get_logger(name: str = None, level: str = "INFO") -> Logger:
    """
    Returns a logger instance for the given name and log level.

    This method ensures that each module can get its own logger with a custom name
    and log level. The default log level is INFO.

    Args:
        name (str, optional): The name of the logger (e.g., module name). Default is None (root logger).
        level (str, optional): The log level as a string (e.g., "DEBUG", "INFO"). Default is "INFO".

    Returns:
        Logger: The configured logger instance for the given module or the root logger.
    """

    # Convert level to the corresponding logging constant
    log_level = _get_log_level(level.upper())

    # Get logger by name, or use root logger if no name is provided
    logger = logging.getLogger(name or __name__)
    logger.setLevel(log_level)

    if not logger.hasHandlers():
        handler = logging.StreamHandler()
        handler.setFormatter(
            logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
        logger.addHandler(handler)

    return logger


def _setup_logging() -> None:
    """
    Configures the logging for the application (private function).

    This function sets up the logging level and format for the application's logs.
    The logging level is determined by the `LOG_LEVEL_DEFAULT` environment variable.
    Default is INFO.

    Additionally, it adds a debug-level log message to indicate that logging has been set up.

    To prevent excessive logging from Azure Monitor and OpenTelemetry, their loggers
    are set to WARNING level regardless of the default log level.

    Returns:
        None
    """

    log_level_str = os.getenv("LOG_LEVEL_DEFAULT", "INFO").upper()

    log_level = _get_log_level(log_level_str)

    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    handler = logging.StreamHandler()
    handler.setFormatter(
        logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    )

    root_logger.addHandler(handler)
    root_logger.info("Logging configured at %s level.", log_level_str)

    # Set higher log level for Azure Monitor and OpenTelemetry loggers to reduce noise
    for logger_name in ["azure", "opencensus", "opentelemetry"]:
        logging.getLogger(logger_name).setLevel(logging.WARNING)


def _setup_open_telemetry(logger: Logger) -> None:
    """
    Configures OpenTelemetry and Azure Monitor for telemetry.

    This function integrates Azure Monitor with OpenTelemetry, using the connection
    string stored in the environment variable `APPLICATIONINSIGHTS_CONNECTION_STRING`.
    If the connection string is found, Azure Monitor is configured; otherwise, a warning
    is logged.

    Args:
        logger (Logger): The logger instance to be used for logging the telemetry configuration.

    Returns:
        None
    """

    try:
        connection_string = os.getenv("APPLICATIONINSIGHTS_CONNECTION_STRING")
        if connection_string:
            configure_azure_monitor()
            logger.info("Azure Monitor successfully configured.")
        else:
            logger.warning(
                "Application Insights Connection String not found. Monitoring not configured."
            )

    except Exception as e:
        logger.error("Failed to configure Azure Monitor: %s", e)


def _get_log_level(level_str: str) -> int:
    """
    Converts a string log level to a logging module constant.

    Args:
        level_str (str): The string representation of the log level (e.g., "DEBUG", "INFO").

    Returns:
        int: The corresponding logging level constant (e.g., logging.DEBUG).
    """

    log_levels = {
        "CRITICAL": logging.CRITICAL,
        "ERROR": logging.ERROR,
        "WARNING": logging.WARNING,
        "INFO": logging.INFO,
        "DEBUG": logging.DEBUG,
        "NOTSET": logging.NOTSET,
    }

    # Return the corresponding log level or default to logging.INFO
    return log_levels.get(level_str, logging.INFO)
