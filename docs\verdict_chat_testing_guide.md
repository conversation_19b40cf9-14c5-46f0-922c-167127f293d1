# Verdict Chat Testing Guide

This guide provides comprehensive testing instructions for the "Start chat with verdict" functionality.

## Overview

The verdict chat functionality allows users to:
1. Seed a chat conversation with verdict information from rechtspraak.nl
2. Start a chat conversation that has context about the verdict
3. Ask questions about the specific verdict

## Architecture

The implementation uses the **seed message approach**:
- Verdict information is added as the first message in the chat using the `/chat/messages/seed` endpoint
- The seeded message has `requires_response=False` to prevent LLM responses
- Duplicate prevention ensures the same verdict isn't added twice
- Chat conversations can then reference the seeded verdict content

## Manual Testing Scenarios

### 1. Basic Verdict Seeding

**Test Case**: Seed a simple verdict message

```bash
curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "test-session-456",
    "content": "ECLI:NL:HR:2023:123\n\nHoge Raad der Nederlanden\nDatum: 15 januari 2023\n\nUitspraak in de zaak...",
    "role": "assistant",
    "verdict_id": "ECLI:NL:HR:2023:123",
    "metadata": {
      "court": "Hoge Raad",
      "date": "2023-01-15"
    }
  }'
```

**Expected Result**: 
- Status: 201 Created
- Response: `{"id": "message-id-123"}`

### 2. Start Chat with Seeded Verdict

**Test Case**: Start a chat conversation after seeding

```bash
curl -X POST "http://localhost:8000/{organization_id}/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-session-456",
    "user_id": "test-user-123",
    "user_message": "Can you explain the key points of this verdict?",
    "jurisprudence": true
  }'
```

**Expected Result**:
- Status: 200 OK
- Response should reference the seeded verdict
- Content should mention "ECLI:NL:HR:2023:123" or "Hoge Raad"

### 3. Duplicate Prevention

**Test Case**: Try to seed the same verdict twice

```bash
# First request (should succeed)
curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "test-session-456",
    "content": "Same verdict content",
    "role": "assistant"
  }'

# Second request with same content (should fail)
curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "test-session-456",
    "content": "Same verdict content",
    "role": "assistant"
  }'
```

**Expected Result**:
- First request: 201 Created
- Second request: 500 Internal Server Error with duplicate message

### 4. Large Verdict Content

**Test Case**: Seed a large verdict (>100KB)

```bash
# Create large content file
python -c "print('A' * 100000)" > large_verdict.txt

curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d "{
    \"conversation_id\": \"large-test-123\",
    \"content\": \"$(cat large_verdict.txt)\",
    \"role\": \"assistant\"
  }"
```

**Expected Result**:
- Should handle large content gracefully
- Response time should be reasonable (<5 seconds)

### 5. Unicode and Special Characters

**Test Case**: Seed verdict with special characters

```bash
curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "unicode-test-123",
    "content": "🏛️ Rechtbank Amsterdam 📅 2023-01-15\n\nUitspraak: €1.000.000 claim\nParties: Müller & Søn B.V. vs Société Française S.A.\n\n中文测试 العربية тест русский",
    "role": "assistant",
    "verdict_id": "ECLI:NL:HR:2023:UNICODE"
  }'
```

**Expected Result**:
- Status: 201 Created
- Unicode characters should be preserved

### 6. Streaming Chat Response

**Test Case**: Test streaming response with verdict context

```bash
curl -X POST "http://localhost:8000/{organization_id}/chat?use_streaming=true" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-session-456",
    "user_id": "test-user-123",
    "user_message": "Summarize this verdict for me",
    "jurisprudence": true
  }'
```

**Expected Result**:
- Content-Type: text/event-stream
- Streaming chunks should reference the verdict

## Error Testing Scenarios

### 1. Invalid Input Validation

```bash
# Missing required fields
curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "test-123"
  }'
```

**Expected**: 422 Validation Error

### 2. Empty Content

```bash
curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "test-123",
    "content": "",
    "role": "assistant"
  }'
```

**Expected**: 422 Validation Error

### 3. Invalid Organization ID

```bash
curl -X POST "http://localhost:8000/invalid-uuid/chat/messages/seed?user_id=test-user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "test-123",
    "content": "Test content",
    "role": "assistant"
  }'
```

**Expected**: 422 Validation Error

## Performance Testing

### 1. Concurrent Requests

Use a tool like Apache Bench or wrk to test concurrent seeding:

```bash
# Create test payload file
echo '{
  "conversation_id": "perf-test-{{.N}}",
  "content": "Performance test verdict content {{.N}}",
  "role": "assistant"
}' > test_payload.json

# Run concurrent tests
ab -n 100 -c 10 -T application/json -p test_payload.json \
  "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=perf-user"
```

### 2. Memory Usage

Monitor memory usage while seeding multiple large verdicts:

```bash
# Monitor memory
top -p $(pgrep -f "uvicorn")

# Seed multiple large verdicts
for i in {1..50}; do
  curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=memory-user" \
    -H "Content-Type: application/json" \
    -d "{
      \"conversation_id\": \"memory-test-$i\",
      \"content\": \"$(python -c 'print("Large verdict content " * 1000)')\",
      \"role\": \"assistant\"
    }"
done
```

## Integration with Rechtspraak Search

### Complete Flow Test

1. **Search for a verdict**:
```bash
curl "http://localhost:8000/{organization_id}/rechtspraak/search?q=contract&top=1"
```

2. **Get verdict details** (mock - would normally fetch from rechtspraak.nl):
```bash
# This would be the actual verdict content fetched by verdict ID
VERDICT_CONTENT="ECLI:NL:HR:2023:123..."
```

3. **Seed the verdict**:
```bash
curl -X POST "http://localhost:8000/{organization_id}/chat/messages/seed?user_id=test-user" \
  -H "Content-Type: application/json" \
  -d "{
    \"conversation_id\": \"rechtspraak-flow-test\",
    \"content\": \"$VERDICT_CONTENT\",
    \"role\": \"assistant\",
    \"verdict_id\": \"ECLI:NL:HR:2023:123\"
  }"
```

4. **Start chat about the verdict**:
```bash
curl -X POST "http://localhost:8000/{organization_id}/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "rechtspraak-flow-test",
    "user_id": "test-user",
    "user_message": "What are the implications of this ruling?",
    "jurisprudence": true
  }'
```

## Automated Test Execution

Run the test suites:

```bash
# Unit tests
pytest test/services/chat/test_seed_message_functionality.py -v

# Integration tests
pytest test/routers/chat/test_seed_message_api.py -v

# End-to-end tests
pytest test/integration/test_verdict_chat_flow.py -v

# Performance tests
pytest test/performance/test_verdict_chat_performance.py -v

# All verdict chat tests
pytest test/ -k "seed_message or verdict_chat" -v
```

## Monitoring and Debugging

### Key Metrics to Monitor

1. **Response Times**:
   - Seed message creation: <1 second
   - Chat response with verdict context: <5 seconds

2. **Memory Usage**:
   - Should not increase significantly with multiple verdicts
   - Large verdicts should be handled efficiently

3. **Error Rates**:
   - Validation errors should be <1% in normal usage
   - Service errors should be <0.1%

### Debug Logging

Enable debug logging to trace the flow:

```python
import logging
logging.getLogger("src.app.services.chat").setLevel(logging.DEBUG)
```

### Common Issues and Solutions

1. **Duplicate verdict error**: Check if the same content was already seeded
2. **Large content timeout**: Increase request timeout settings
3. **Unicode encoding issues**: Ensure proper UTF-8 encoding
4. **Memory leaks**: Monitor Cosmos DB connection pooling

## Success Criteria

The implementation is considered successful if:

✅ **Functional Requirements**:
- Users can seed verdict information into chat conversations
- Chat responses can reference the seeded verdict content
- Duplicate verdicts are prevented
- Both streaming and non-streaming responses work

✅ **Performance Requirements**:
- Seed message creation: <1 second for normal content
- Large content (1MB): <5 seconds
- Concurrent requests: Handle 10+ simultaneous requests
- Memory usage: Stable with multiple verdicts

✅ **Quality Requirements**:
- Comprehensive test coverage (>90%)
- Error handling for all edge cases
- Unicode and special character support
- Proper validation and security measures
