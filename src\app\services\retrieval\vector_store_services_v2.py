"""
This module defines services for vector store operations.
"""

# Standard imports
import logging
import os
import tempfile
import uuid
from enum import Enum
from typing import Dict, List, Optional, Tuple
from uuid import UUID

# External imports
from azure.core import MatchConditions
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import ResourceNotFoundError
from azure.search.documents.indexes.aio import SearchIndexClient
from azure.search.documents.indexes.models import (
    SearchField,
    SearchFieldDataType,
    SearchableField,
    SimpleField,
)

from langchain_core.documents import Document
from langchain_community.document_loaders import (
    PyMuPDFLoader,
    UnstructuredWordDocumentLoader,
)

from langchain_community.vectorstores.azuresearch import AzureSearch
from langchain_openai import AzureOpenAIEmbeddings
from langchain_text_splitters import CharacterTextSplitter

# Internal imports
from src.app.models.retrieval.vector_store_models_v2 import (
    ProjectIngestResponse,
    IngestDocumentRequest,
    DocumentProcessingError,
)

# from src.app.services.retrieval.index_services import IndexService
from src.app.services.retrieval.storage_services_v2 import (
    BlobStorageService,
    StorageService,
)


class SupportedFileType(Enum):
    """
    Enum representing supported file types.
    """

    DOCX = ".docx"
    PDF = ".pdf"


class VectorStoreService:
    """
    Service for loader service for projects.
    """

    BLOB_SOURCE_DIRECTORY_NAME = "documents"
    BLOB_INDEXD_DIRECTORY_NAME = "indexed"

    def __init__(
        self,
        organization_id: UUID,
        project_id: UUID,
        storage_service: Optional[StorageService] = None,
        # index_service: Optional[IndexService] = None,
        lazy_init: bool = False,
    ):
        self.organization_id = organization_id
        self.project_id = project_id
        self._lazy_init = lazy_init
        # self._index_service = index_service
        self._storage_service = storage_service
        self._embeddings_client = None
        self._index_client = None
        self._search_client = None
        self._init_env_variables()

    async def __aenter__(self) -> "VectorStoreService":
        """
        Enter the runtime context related to this object.

        This method is called when entering the context manager. It initializes
        the resource clients if lazy initialization is not enabled.

        Returns:
            VectorStoreService: The instance of the service.
        """

        logging.info("Info: Entering context manager for VectorStoreService.")

        if not self._lazy_init:
            await self._init_resource_clients()

        return self

    async def __aexit__(self, exc_type, exc, tb) -> None:
        """
        Exit the runtime context related to this object.

        This method is called when exiting the context manager. It closes
        the resource clients that were initialized.

        Args:
            exc_type (type): The exception type, if an exception was raised.
            exc (Exception): The exception instance, if an exception was raised.
            tb (traceback): The traceback object, if an exception was raised.
        """

        logging.info("Info: Exiting context manager for VectorStoreService.")

        await self._close_resource_clients()

    async def delete_document(self, document_id: UUID, file_name: str) -> None:
        """
        Deletes a specific document from both the Azure Blob Storage and the Azure Search Index.

        Args:
            document_id (UUID): The ID of the document to delete.
            file_name (str): The name of the file (with path) to delete.

        Raises:
            ResourceNotFoundError: If the document is not found in either Azure Blob Storage or Azure Search Index.
        """

        try:
            container_name = f"{self.container_prefix}{self.organization_id}"
            blob_name = f"{self._get_ingestion_paths()[0]}/{file_name}"

            try:
                if self._lazy_init:
                    await self._init_resource_clients()
                blob_properties = await self._storage_service.get_blob_properties(
                    container_name, blob_name
                )

                blob_metadata = blob_properties["metadata"]
                if str(document_id) != blob_metadata.get("document_id"):
                    logging.warning(
                        "Document ID in metadata does not match the provided document_id."
                    )
                    raise ResourceNotFoundError(
                        f"Document ID {document_id} not found in Blob Storage metadata."
                    )

                await self._storage_service.delete_blob(container_name, blob_name)
                logging.info(
                    "Document %s successfully deleted from Blob Storage.",
                    str(document_id),
                )

            except ResourceNotFoundError as e:
                logging.warning(
                    "File %s not found in Blob Storage. Which is okay, because we're trying to delete it.",
                    file_name,
                )
                raise ResourceNotFoundError(
                    f"File {file_name} not found in Blob Storage."
                ) from e

            try:
                documents = await self._search_client.asimilarity_search(
                    query="*",
                    filters=f"document_id eq '{str(document_id)}'",
                    k=self._search_top_k_results,
                )

                ids_to_delete = [doc.metadata["id"] for doc in documents]
                if ids_to_delete:
                    await self._search_client.adelete(
                        ids=ids_to_delete
                    )  # TODO: Verificar versão do fix https://github.com/langchain-ai/langchain/pull/22315

                    logging.info(
                        "Successfully deleted %s documents with document_id %s from Azure Search.",
                        len(ids_to_delete),
                        str(document_id),
                    )
                else:
                    logging.warning(
                        "No documents found in Azure Search with document_id %s.",
                        str(document_id),
                    )

            except Exception as e:
                logging.error(
                    "Failed to delete document %s from Azure Search: %s",
                    str(document_id),
                    str(e),
                )
                # Por enquanto, caso ocorra algum erro ao tentar excluir do AI Search, não levanta a exceção, pois o blob foi excluído
                logging.warning(
                    "Document %s was successfully deleted from Blob Storage but failed to delete from Search.",
                    str(document_id),
                )

        except Exception as e:
            logging.error("Error deleting document %s: %s", str(document_id), str(e))
            raise e

    async def delete_vector_store(self, project_id: UUID = None) -> None:
        """
        Deletes the vector store.

        Args:
            project_id (str, optional): The project ID. Defaults to None.
        """
        index_name = (
            f"{self.index_prefix}{project_id}"
            if project_id
            else self.index_prefix + str(self.project_id)
        )
        await self._delete_index(index_name)

    async def create_vector_store(self) -> None:
        """
        Creates the vector store.

        Args:
            project_id (str, optional): The project ID.
        """

        await self._init_search_client()

    async def vector_store_exists(self) -> bool:
        """
        Checks if the vector store exists for the current project.

        Returns:
            bool: True if the vector store exists, False otherwise.
        """
        if not self._index_client:
            await self._init_index_client()
        index_name = f"{self.index_prefix}{self.project_id}"
        index_names = [name async for name in self._index_client.list_index_names()]
        return index_name in index_names

    def get_fields(self) -> List[SearchField]:
        """
        Defines and returns the search fields for the index.

        This method creates a list of fields that are used in the search index.
        Each field is defined with specific attributes such as name, type, and
        whether it is searchable, filterable, or a key field.

        Returns:
            List[SearchField]: A list of field definitions for the search index.
        """

        return [
            SimpleField(
                name="id",
                type=SearchFieldDataType.String,
                key=True,
                filterable=True,
            ),
            SearchableField(
                name="content",
                type=SearchFieldDataType.String,
                searchable=True,
            ),
            SearchField(
                name="content_vector",
                type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                searchable=True,
                vector_search_dimensions=1536,
                vector_search_profile_name="myHnswProfile",
            ),
            SearchableField(
                name="metadata",
                type=SearchFieldDataType.String,
                searchable=True,
            ),
            SearchableField(
                name="document_id",
                type=SearchFieldDataType.String,
                searchable=True,
                filterable=True,
            ),
            SearchableField(
                name="document_type",
                type=SearchFieldDataType.String,
                searchable=True,
                filterable=True,
            ),
            SimpleField(
                name="project_id",
                type=SearchFieldDataType.String,
                filterable=True,
            ),
        ]

    async def _process_and_copy_blob(
        self,
        blob_name: str,
        blob_metadata: dict,
        destination_path: str,
    ) -> Tuple[
        int, int
    ]:  # TODO: Issue 18 | Single blob processing logic should be improved + Too many arguments
        """
        Processes and copies a specific blob using StorageService.

        Args:
            blob_name: Name of the blob to process (including path)
            blob_metadata: Metadata associated with the blob
            destination_path: Destination path to copy the blob after processing

        Returns:
            Tuple containing (number of documents processed, number of blobs processed)
        """

        container_name = f"{self.container_prefix}{self.organization_id}"
        blob_content = await self._storage_service.download_blob(
            container_name, blob_name
        )

        documents = await self._process_content(blob_content, blob_name, blob_metadata)
        split_documents = await self._split_documents(documents)

        try:
            # Index the documents
            await self._index_documents(split_documents)

            # Upload usando StorageService
            new_blob_name = f"{destination_path}/{os.path.basename(blob_name)}"
            await self._storage_service.upload_blob(
                container_name, new_blob_name, blob_content, blob_metadata
            )

            return len(split_documents), 1

        except Exception as e:
            logging.error(
                "Error indexing documents: %s", e
            )  # TODO: Issue 25 | Log an error should include detailed information about the exception
            raise e

    async def ingest_documents(
        self, documents: Optional[List[IngestDocumentRequest]] = None
    ) -> ProjectIngestResponse:
        """
        Ingests all documents from the source directory into the vector store.

        Uses StorageService for blob operations and processes each valid document.

        Returns:
            ProjectIngestResponse containing ingestion metrics
        """

        if self._lazy_init:
            logging.info("Info: Lazy initialization of resources.")
            await self._init_resource_clients()

        container_name = f"{self.container_prefix}{self.organization_id}"
        source_path, destination_path = self._get_ingestion_paths()
        successfully_processed = []
        processing_errors = []
        all_split_docs = []
        total_docs = 0
        total_blobs = 0
        if documents:
            for document in documents:
                blob_name = f"{source_path}/{document.file_name}"
                try:
                    # Get and validate metadata
                    properties = await self._storage_service.get_blob_properties(
                        container_name, blob_name
                    )
                    metadata = properties.get("metadata")
                    if not metadata:
                        processing_errors.append(
                            DocumentProcessingError(
                                document_id=uuid.UUID(document.id),
                                error_message="Blob metadata is empty.",
                                error_type="MetadataError",
                            )
                        )
                        continue

                    if str(document.id) != metadata["document_id"]:
                        processing_errors.append(
                            DocumentProcessingError(
                                document_id=uuid.UUID(document.id),
                                error_message="Document ID does not match document metadata.",
                                error_type="MetadataError",
                            )
                        )
                        continue

                    # 1. Download content
                    content = await self._storage_service.download_blob(
                        container_name, blob_name
                    )

                    # 2. Process and split documents
                    documents = await self._process_content(
                        content, blob_name, metadata
                    )
                    all_split_docs += await self._split_documents(documents)

                    # 4. Move to destination
                    new_blob_name = f"{destination_path}/{document.file_name}"
                    await self._storage_service.upload_blob(
                        container_name, new_blob_name, content, metadata
                    )
                    successfully_processed.append(document.id)
                    total_blobs += 1
                except Exception as e:
                    logging.error("Error processing blob %s: %s", blob_name, str(e))
                    processing_errors.append(
                        DocumentProcessingError(
                            document_id=UUID(document.id),
                            error_message=str(e),
                            error_type="ProcessingError",
                        )
                    )
                    continue

            # 3. Index all documents combined
            await self._index_documents(all_split_docs)
            total_docs += len(all_split_docs)
        else:
            async for blob in self._storage_service.list_blobs(
                container_name, prefix=source_path, include_datasets="metadata"
            ):
                if not blob.metadata:
                    processing_errors.append(
                        DocumentProcessingError(
                            document_id=UUID(int=0),
                            error_message="Blob metadata is empty.",
                            error_type="MetadataError",
                        )
                    )
                    continue

                if not (
                    "document_id" in blob.metadata and "project_id" in blob.metadata
                ):
                    processing_errors.append(
                        DocumentProcessingError(
                            document_id=(
                                blob.metadata["document_id"]
                                if "document_id" in blob.metadata
                                else UUID(int=0)
                            ),
                            error_message="Blob metadata does not contain document or project id.",
                            error_type="MetadataError",
                        )
                    )
                    continue
                try:
                    docs_ingested, blobs_proc = await self._process_and_copy_blob(
                        blob.name, blob.metadata, destination_path
                    )

                    total_docs += docs_ingested
                    total_blobs += blobs_proc
                    successfully_processed.append(blob.metadata["document_id"])
                except Exception as e:
                    logging.error("Error processing blob %s: %s", blob.name, str(e))
                    processing_errors.append(
                        DocumentProcessingError(
                            document_id=blob.metadata["document_id"],
                            error_message=str(e),
                            error_type="ProcessingError",
                        )
                    )

        return ProjectIngestResponse(
            id=uuid.uuid4(),
            project_id=self.project_id,
            organization_id=self.organization_id,
            blobs_processed=total_blobs,
            documents_ingested=total_docs,
            successfully_processed=successfully_processed,
            processing_errors=processing_errors,
        )

    async def ingest_document(
        self, document_id: UUID, file_path: str
    ) -> ProjectIngestResponse:
        """
        Ingests a specific document into the vector store.

        Args:
            document_id: UUID of the document to ingest
            file_path: Relative path of the document in storage

        Returns:
            ProjectIngestResponse with ingestion details

        Raises:
            ResourceNotFoundError: If document or metadata is invalid
            Exception: For any processing errors during ingestion
        """

        # Initialize resources if lazy loading
        if self._lazy_init:
            logging.info("Initializing resources lazily")
            await self._init_resource_clients()

        # Prepare paths
        container_name = f"{self.container_prefix}{self.organization_id}"
        source_path, destination_path = self._get_ingestion_paths()
        blob_name = f"{source_path}/{file_path}"
        processing_errors = []
        all_split_docs = []
        total_docs = 0
        try:
            # Get and validate metadata
            properties = await self._storage_service.get_blob_properties(
                container_name, blob_name
            )
            metadata = properties.get("metadata")
            if not metadata:
                processing_errors.append(
                    DocumentProcessingError(
                        document_id=UUID(document_id),
                        error_message="Blob metadata is empty.",
                        error_type="MetadataError",
                    )
                )
            elif str(document_id) != metadata["document_id"]:
                processing_errors.append(
                    DocumentProcessingError(
                        document_id=UUID(document_id),
                        error_message="Document ID does not match document metadata.",
                        error_type="MetadataError",
                    )
                )
            else:

                # 1. Download content
                content = await self._storage_service.download_blob(
                    container_name, blob_name
                )

                # 2. Process and split documents
                documents = await self._process_content(content, blob_name, metadata)
                all_split_docs += await self._split_documents(documents)

                # 3. Index documents
                await self._index_documents(all_split_docs)

                # 4. Move to destination
                new_blob_name = f"{destination_path}/{file_path}"
                await self._storage_service.upload_blob(
                    container_name, new_blob_name, content, metadata
                )
                total_docs += len(all_split_docs)

        except Exception as e:
            logging.error("Error processing blob %s: %s", blob_name, str(e))
            processing_errors.append(
                DocumentProcessingError(
                    document_id=document_id,
                    error_message=str(e),
                    error_type="ProcessingError",
                )
            )

        return ProjectIngestResponse(
            id=uuid.uuid4(),
            project_id=self.project_id,
            organization_id=self.organization_id,
            blobs_processed=1 if processing_errors == [] else 0,
            documents_ingested=total_docs,
            successfully_processed=[document_id] if processing_errors == [] else [],
            processing_errors=processing_errors,
        )

    def _init_env_variables(self) -> None:
        self.search_endpoint = os.getenv("AZURE_SEARCH_ENDPOINT")
        self.search_admin_key = os.getenv("AZURE_SEARCH_ADMIN_KEY")
        self.openai_deployment = os.getenv("AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT_NAME")
        self.openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self.openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self.openai_api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        self.storage_account_name = os.getenv("AZURE_STORAGE_ACCOUNT_NAME")
        self.storage_account_key = os.getenv("AZURE_STORAGE_ACCOUNT_KEY")
        self.index_prefix = os.getenv("AZURE_SEARCH_INDEX_PREFIX", "idx-")
        self.indexer_prefix = os.getenv("AZURE_SEARCH_INDEXER_PREFIX", "idr-")
        self.datasource_prefix = os.getenv("AZURE_SEARCH_DATASOURCE_PREFIX", "dsc-")
        self.storage_account_prefix = os.getenv(
            "AZURE_STORAGE_ACCOUNT_PATH_PREFIX", "prj-"
        )
        self.container_prefix = os.getenv(
            "AZURE_STORAGE_ACCOUNT_CONTAINER_PREFIX", "org-"
        )
        self._search_top_k_results = int(
            os.getenv("AZURE_SEARCH_TOP_K_RESULTS", "50")
        )  # TODO: Padronizar inicialização de variáveis conforme outros casos

    async def _init_index_client(self) -> None:
        """
        Initializes the index client.
        """

        if not self._index_client:
            self._index_client = SearchIndexClient(
                endpoint=self.search_endpoint,
                credential=AzureKeyCredential(self.search_admin_key),
            )

    async def _init_search_client(self) -> None:
        """
        Initializes the search client.
        """

        if not self._search_client:
            self._embeddings_client = AzureOpenAIEmbeddings(
                azure_deployment=self.openai_deployment,
                api_key=self.openai_api_key,
                azure_endpoint=self.openai_endpoint,
                api_version=self.openai_api_version,
            )

            self._search_client = AzureSearch(
                azure_search_endpoint=self.search_endpoint,
                azure_search_key=self.search_admin_key,
                index_name=self.index_prefix + str(self.project_id),
                fields=self.get_fields(),
                embedding_function=self._embeddings_client.embed_query,
            )

    async def _init_resource_clients(self) -> None:
        """
        Initializes required service clients.

        Initializes StorageService (if not already initialized), SearchIndexClient,
        AzureOpenAIEmbeddings and AzureSearch client.
        """

        logging.info("Info: Initializing resource clients.")

        if not self._storage_service:
            self._storage_service = BlobStorageService(
                self.storage_account_name,
                self.storage_account_key,
            )

        if not self._index_client:
            self._index_client = SearchIndexClient(
                endpoint=self.search_endpoint,
                credential=AzureKeyCredential(self.search_admin_key),
            )

        if not self._search_client:
            self._embeddings_client = AzureOpenAIEmbeddings(
                azure_deployment=self.openai_deployment,
                api_key=self.openai_api_key,
                azure_endpoint=self.openai_endpoint,
                api_version=self.openai_api_version,
            )

            self._search_client = AzureSearch(
                azure_search_endpoint=self.search_endpoint,
                azure_search_key=self.search_admin_key,
                index_name=self.index_prefix + str(self.project_id),
                fields=self.get_fields(),
                embedding_function=self._embeddings_client.embed_query,
            )

    async def _close_resource_clients(self) -> None:
        """
        Closes the resource clients for blob storage and search index.
        """

        if self._index_client:
            await self._index_client.close()

        if self._search_client:
            self._search_client.client.close()

    def _init_search_service(self) -> None:
        """
        Initializes the search service with Azure OpenAI embeddings.
        """

        logging.info("Info: Initializing search service.")

        if not self._search_client:
            embeddings = AzureOpenAIEmbeddings(
                azure_deployment=self.openai_deployment,
                api_key=self.openai_api_key,
                azure_endpoint=self.openai_endpoint,
                api_version=self.openai_api_version,
            )

            self._search_client = AzureSearch(
                azure_search_endpoint=self.search_endpoint,
                azure_search_key=self.search_admin_key,
                index_name=self.index_prefix + str(self.project_id),
                fields=self.get_fields(),
                embedding_function=embeddings.embed_query,
            )

    async def _delete_index(self, index_name: str) -> None:
        """
        Deletes the index from Azure AI Search.

        Args:
            index_name (str): The name of the index to delete.
        """

        try:
            if not self._index_client:
                await self._init_index_client()

            index = await self._index_client.get_index(index_name)

            await self._index_client.delete_index(
                index, match_condition=MatchConditions.IfNotModified
            )

        except ResourceNotFoundError:
            logging.error("Index not found: %s", index_name)
            raise

        except Exception as e:
            logging.error("Error deleting index: %s", e)
            raise

    def _get_ingestion_paths(self) -> Tuple[str, str]:
        """
        Returns the documents and indexed paths for the current project.

        Returns:
            Tuple[str, str]: The documents path and indexed path.
        """

        project_path = f"{self.storage_account_prefix}{self.project_id}"
        return (
            f"{project_path}/{self.BLOB_SOURCE_DIRECTORY_NAME}",
            f"{project_path}/{self.BLOB_INDEXD_DIRECTORY_NAME}",
        )

    async def _copy_blob_to_diretory(
        self,
        blob_name: str,
        content: bytes,
        metadata: Dict,
        path: str,
        overwrite: bool = True,
    ) -> None:
        """
        Copies a blob to a new directory using StorageService.

        Args:
            blob_name: Original blob name (including path)
            content: Blob content as bytes
            metadata: Dictionary with blob metadata
            path: Destination path
            overwrite: Whether to overwrite if blob exists (default True)
        """

        container_name = f"{self.container_prefix}{self.organization_id}"
        new_blob_name = f"{path}/{os.path.basename(blob_name)}"

        # TODO: Improve this service, a lot of rushed code was used in these methods, it needs to be refactored
        clean_metadata = {
            k: v for k, v in metadata.items() if v not in (None, "", [], {})
        }

        await self._storage_service.upload_blob(
            container_name,
            new_blob_name,
            content,
            clean_metadata,
            overwrite,
        )

    async def _process_content(
        self, content: bytes, blob_name: str, metadata: Dict[str, str]
    ) -> List[Document]:
        """
        Processes the content of a blob based on its type (PDF or DOCX).

        Args:
            content (bytes): The content of the blob.
            blob_name (str): The name of the blob.
            metadata (Dict[str, str]): The metadata of the blob.

        Returns:
            List[Document]: A list of processed documents.
        """

        try:
            return await self._load_into_documents(content, blob_name, metadata)

        except ValueError as e:
            logging.error("Error processing content: %s", e)
            raise

    async def _load_into_documents(
        self, content: bytes, blob_name: str, metadata: Dict[str, str]
    ) -> List[Document]:
        """
        Loads the content of a file into documents based on its extension (PDF or DOCX),
        and processes the documents accordingly.

        Args:
            content (bytes): The content of the blob.
            blob_name (str): The name of the blob.
            metadata (Dict[str, str]): The metadata of the blob.

        Returns:
            List[Document]: A list of processed documents.
        """
        documents = []
        file_extension = os.path.splitext(blob_name)[1].lower()

        try:
            # Create a temporary file to store the content
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=file_extension
            ) as tmp_file:
                tmp_file.write(content)
                tmp_file_path = tmp_file.name

            # PDF processing logic
            if file_extension == SupportedFileType.PDF.value:
                loader = PyMuPDFLoader(file_path=tmp_file_path, extract_images=False)
                documents = loader.load()

                # If all pages are empty, try extracting images
                if all(not doc.page_content for doc in documents):
                    loader = PyMuPDFLoader(file_path=tmp_file_path, extract_images=True)
                    documents = loader.load()

            # DOCX processing logic
            elif file_extension == SupportedFileType.DOCX.value:
                loader = UnstructuredWordDocumentLoader(file_path=tmp_file_path)
                documents = loader.load()

            else:
                raise ValueError(f"Unsupported file extension: {file_extension}")

            # Update metadata for each document
            for document in documents:
                document.metadata.update(metadata)
                document.metadata["source"] = os.path.basename(blob_name)
                document.metadata["file_path"] = blob_name

        except Exception as e:
            logging.error("Error loading document: %s", e)
            raise

        finally:
            # Clean up the temporary file
            try:
                os.remove(tmp_file_path)

            except OSError as e:
                logging.error("Error removing temporary file: %s", e)

        return documents

    async def _split_documents(
        self, documents: List[Document], chunk_size: int = 1000, chunk_overlap: int = 0
    ) -> List[Document]:
        """
        Splits documents into smaller chunks.

        Args:
            documents (List[Document]): The list of documents to split.
            chunk_size (int, optional): The size of the chunks to split the documents into. Defaults to 1000.
            chunk_overlap (int, optional): The size of the overlap between chunks. Defaults to 0.

        Returns:
            List[Document]: A list of split documents.
        """

        try:
            text_splitter = CharacterTextSplitter(
                chunk_size=chunk_size, chunk_overlap=chunk_overlap
            )

            split_documents = text_splitter.split_documents(documents)
            return split_documents

        except Exception as e:
            logging.error("Error splitting documents: %s", e)
            raise

    async def _index_documents(self, documents: List[Document]):
        """
        Indexes the given documents using the search service.

        Args:
            documents: The documents to index.

        Raises:
            Exception: If indexing fails.
        """
        result = self._search_client.add_documents(documents=documents)
        if not result:
            raise Exception(f"Error indexing documents: {documents}")
