"""
This module defines FastAPI routers for vector store operations.
"""

# Standard imports
from uuid import UUID, uuid4
from typing import List

# External imports
from azure.core.exceptions import HttpResponseError, ResourceNotFoundError
from fastapi import APIRouter, Body, Path, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from opentelemetry.trace import SpanKind
from opentelemetry.propagate import extract

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v1 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.models.retrieval.vector_store_models_v1 import (
    ProjectDocumentDeleteResponse,
    ProjectDocumentRequest,
    ProjectDocumentsDeleteRequest,
    ProjectDocumentsDeleteResponse,
    ProjectIngestResponse,
    ProjectVectorStoreCreateResponse,
    ProjectVectorStoreDeleteResponse,
    ProjectVectorStoreExistsResponse,
    IngestDocumentRequest,
)

from src.app.services.retrieval.vector_store_services_v1 import VectorStoreService


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


@api_router.post(
    "/{organization_id}/projects/{project_id}/create",
    description="Creates a vector store for a specific project.",
    operation_id="create_project_vector_store",
    response_description="Confirmation of vector store creation.",
    response_model=ProjectVectorStoreCreateResponse,
    summary="Creates a project vector store. ☑️",
)
async def create_project_vector_store(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
) -> JSONResponse:
    """
    Creates a vector store for a specific project.

    This endpoint creates a new vector store for the project identified by `project_id`.
    It returns a confirmation response indicating the vector store creation status.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        request (Request): The request object that holds metadata about the HTTP request.

    Returns:
        JSONResponse: A response containing the status of the vector store creation.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                await vector_store_service.create_vector_store()

            response = ProjectVectorStoreCreateResponse(
                id=uuid4(),
                project_id=project_id,
                organization_id=organization_id,
                message="Vector store created successfully.",
            )

            return JSONResponse(
                content=jsonable_encoder(response),
                status_code=status.HTTP_201_CREATED,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "creating vector store")


@api_router.delete(
    "/{organization_id}/projects/{project_id}/documents/{document_id}/delete",
    description="Deletes a specific document from the vector store of a specific project.",
    operation_id="delete_project_document_from_vector_store",
    response_description="Confirmation of document deletion from the vector store.",
    summary="Deletes a document from the project vector store. ☑️",
    response_model=ProjectDocumentDeleteResponse,
    tags=["documents"],
)
async def delete_project_document(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
    document_id: UUID = Path(
        ..., description="The unique identifier of the document to be deleted."
    ),
    request_body: ProjectDocumentRequest = Body(
        ..., description="The request body containing document details."
    ),
) -> JSONResponse:
    """
    Deletes a specific document from the vector store of a specific project.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        document_id (UUID): The unique identifier of the document to be deleted.
        request (Request): The request object that holds metadata about the HTTP request.
        request_body (ProjectDocumentRequest): The body of the request containing details of the document.

    Returns:
        JSONResponse: A response containing the status of the document deletion.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                if not await vector_store_service.vector_store_exists():
                    raise ResourceNotFoundError(
                        f"Vector store for project {project_id} not found."
                    )
                await vector_store_service.delete_document(
                    document_id, request_body.file_name
                )

            response = ProjectDocumentDeleteResponse(
                id=uuid4(),
                project_id=project_id,
                organization_id=organization_id,
                document_id=document_id,
                message="Document deleted successfully.",
            )

            return JSONResponse(
                content=jsonable_encoder(response),
                status_code=status.HTTP_200_OK,
            )

    except Exception as e:
        return _handle_error(
            e,
            organization_id,
            project_id,
            "deleting document with id: " + str(document_id),
        )


@api_router.post(
    "/{organization_id}/projects/{project_id}/documents/delete",
    description="Deletes multiple documents from the vector store of a specific project.",
    operation_id="delete_project_documents_from_vector_store",
    response_description="Confirmation of documents deletion from the vector store.",
    summary="Deletes multiple documents from the project vector store. ☑️",
    response_model=ProjectDocumentsDeleteResponse,
    tags=["documents"],
)
async def delete_project_documents(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
    request_body: ProjectDocumentsDeleteRequest = Body(
        ..., description="The request body containing documents to delete."
    ),
) -> JSONResponse:
    """
    Deletes multiple documents from the vector store of a specific project.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        request (Request): The request object that holds metadata about the HTTP request.
        request_body (ProjectDocumentsDeleteRequest): The body of the request containing details of the documents.

    Returns:
        JSONResponse: A response containing the status of the document deletions.
    """
    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                if not await vector_store_service.vector_store_exists():
                    raise ResourceNotFoundError(
                        f"Vector store for project {project_id} not found."
                    )
                successful_deletions = []
                failed_deletions = []

                for doc in request_body.documents:
                    try:
                        await vector_store_service.delete_document(
                            doc.document_id, doc.file_name
                        )
                        successful_deletions.append(
                            {
                                "document_id": doc.document_id,
                                "file_name": doc.file_name,
                                "success": True,
                                "message": "Document deleted successfully",
                            }
                        )
                    except Exception as e:
                        failed_deletions.append(
                            {
                                "document_id": doc.document_id,
                                "file_name": doc.file_name,
                                "success": False,
                                "message": str(e),
                            }
                        )

                response = ProjectDocumentsDeleteResponse(
                    id=uuid4(),
                    project_id=project_id,
                    organization_id=organization_id,
                    successful_deletions=successful_deletions,
                    failed_deletions=failed_deletions,
                )

                return JSONResponse(
                    content=jsonable_encoder(response),
                    status_code=status.HTTP_200_OK,
                )

    except Exception as e:
        return _handle_error(
            e,
            organization_id,
            project_id,
            "Deleting document with id: " + str(request_body.documents),
        )


@api_router.delete(
    "/{organization_id}/projects/{project_id}/delete",
    description="Delete a vector store of a specific project.",
    operation_id="delete_project_vector_store",
    response_description="Confirmation of vector store deletion.",
    response_model=ProjectVectorStoreDeleteResponse,
    summary="Deletes a project vector store. ☑️",
)
async def delete_project_vector_store(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
) -> JSONResponse:
    """
    Deletes a vector store of a specific project.

    This endpoint deletes the vector store associated with a project. It returns a
    confirmation message indicating the deletion status.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        request (Request): The request object that holds metadata about the HTTP request.

    Returns:
        JSONResponse: A response containing the status of the vector store deletion.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                if not await vector_store_service.vector_store_exists():
                    api_logger.warning(
                        "Trying to delete a vector store that does not exist."
                    )
                    response = ProjectVectorStoreDeleteResponse(
                        id=uuid4(),
                        project_id=project_id,
                        organization_id=organization_id,
                        message="Vector store was already deleted or does not exist. No action taken.",
                    )

                    return JSONResponse(
                        content=jsonable_encoder(response),
                        status_code=status.HTTP_404_NOT_FOUND,
                    )

                await vector_store_service.delete_vector_store(project_id)
                response = ProjectVectorStoreDeleteResponse(
                    id=uuid4(),
                    project_id=project_id,
                    organization_id=organization_id,
                    message="Vector store deleted successfully.",
                )

                return JSONResponse(
                    content=jsonable_encoder(response),
                    status_code=status.HTTP_200_OK,
                )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "deleting vector store")


@api_router.post(
    "/{organization_id}/projects/{project_id}/ingest",
    description="Ingests data into the vector store for a specific project, supporting PDF and DOCX files.",
    operation_id="ingest_project_data",
    response_description="The ingestion information for the project.",
    response_model=ProjectIngestResponse,
    summary="Data ingestion for a project. ☑️",
)
async def ingest_project_data(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
) -> JSONResponse:
    """
    Ingests data into the vector store of a specific project.

    This endpoint processes the data ingestion request for a project, typically involving
    PDF or DOCX files. It returns a JSONResponse with the ingestion information.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        request (Request): The request object that holds metadata about the HTTP request.

    Returns:
        JSONResponse: A response containing the status and details of the ingestion process.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                if not await vector_store_service.vector_store_exists():
                    raise ResourceNotFoundError(
                        f"Vector store for project {project_id} not found."
                    )
                response = await vector_store_service.ingest_documents()

            return JSONResponse(
                content=jsonable_encoder(response),
                status_code=status.HTTP_201_CREATED,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "ingesting all documents")


@api_router.post(
    "/{organization_id}/projects/{project_id}/documents/ingest",
    description="Ingests data into the vector store for a specific project, supporting PDF and DOCX files.",
    operation_id="ingest_multiple_documents_data",
    response_description="The ingestion information for the project.",
    response_model=ProjectIngestResponse,
    summary="Data ingestion for selected files for a project. ☑️",
)
async def ingest_multiple_documents_data(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
    request_body: List[IngestDocumentRequest] = Body(
        ..., description="The request body containing as list of document id and name."
    ),
) -> JSONResponse:
    """
    Ingests data into the vector store of a specific project.

    This endpoint processes the data ingestion request for a project, typically involving
    PDF or DOCX files. It returns a JSONResponse with the ingestion information.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        request (Request): The request object that holds metadata about the HTTP request.

    Returns:
        JSONResponse: A response containing the status and details of the ingestion process.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                if not await vector_store_service.vector_store_exists():
                    raise ResourceNotFoundError(
                        f"Vector store for project {project_id} not found."
                    )
                response = await vector_store_service.ingest_documents(request_body)

            return JSONResponse(
                content=jsonable_encoder(response),
                status_code=status.HTTP_201_CREATED,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, project_id, "ingesting a list of documents"
        )


@api_router.post(
    "/{organization_id}/projects/{project_id}/documents/{document_id}/ingest",
    description="Ingests a specific document into the vector store for a project, supporting PDF and DOCX files.",
    operation_id="ingest_project_document_data",
    response_description="The ingestion information for the document.",
    response_model=ProjectIngestResponse,
    summary="Data ingestion for a single document. ☑️",
    tags=["documents"],
)
async def ingest_project_document_data(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
    document_id: UUID = Path(
        ..., description="The unique identifier of the document being ingested."
    ),
    request_body: ProjectDocumentRequest = Body(
        ..., description="The request body containing document details."
    ),
) -> JSONResponse:
    """
    Ingests a specific document into the vector store of a specific project.

    This endpoint processes the ingestion of a specific document (identified by `document_id`)
    into the project vector store. It returns the ingestion status and details for the document.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        document_id (UUID): The unique identifier of the document being ingested.
        request (Request): The request object that holds metadata about the HTTP request.
        request_body (ProjectDocumentRequest): The body of the request containing details of the document.

    Returns:
        JSONResponse: A response containing the status and details of the document ingestion process.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                if not await vector_store_service.vector_store_exists():
                    raise ResourceNotFoundError(
                        f"Vector store for project {project_id} not found."
                    )
                response = await vector_store_service.ingest_document(
                    document_id, request_body.file_name
                )

            return JSONResponse(
                content=jsonable_encoder(response),
                status_code=status.HTTP_201_CREATED,
            )

    except Exception as e:
        return _handle_error(
            e,
            organization_id,
            project_id,
            "ingesting document with Id: " + str(document_id),
        )


@api_router.get(
    "/{organization_id}/projects/{project_id}/exists",
    description="Checks if a vector store exists for a specific project.",
    operation_id="check_project_vector_store_exists",
    response_description="Status of vector store existence.",
    response_model=ProjectVectorStoreExistsResponse,
    summary="Checks if a project vector store exists. ☑️",
)
async def check_project_vector_store_exists(
    request: Request,
    organization_id: UUID = Path(
        ..., description="The unique identifier of the organization."
    ),
    project_id: UUID = Path(..., description="The unique identifier of the project."),
) -> JSONResponse:
    """
    Checks if a vector store exists for a specific project.

    Args:
        organization_id (UUID): The unique identifier of the organization.
        project_id (UUID): The unique identifier of the project.
        request (Request): The request object that holds metadata about the HTTP request.

    Returns:
        JSONResponse: A response containing whether the vector store exists.
    """
    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            async with VectorStoreService(
                organization_id, project_id, lazy_init=True
            ) as vector_store_service:
                exists = await vector_store_service.vector_store_exists()

            response = ProjectVectorStoreExistsResponse(
                id=uuid4(),
                project_id=project_id,
                organization_id=organization_id,
                exists=exists,
                message="Vector store check completed successfully.",
            )

            return JSONResponse(
                content=jsonable_encoder(response),
                status_code=status.HTTP_200_OK,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "vector store check")


def _handle_error(e: Exception, organization_id: UUID, project_id: UUID, action: str):
    """
    Handles the error by logging it and raising an HTTPException with a suitable status code.

    Args:
        e (Exception): The exception that was raised.
        document_ids (List[UUID]): The list of document IDs to include.
        project_id (UUID): The ID of the project that caused the error.
        action (str): A string describing the action being attempted (e.g., 'description', 'questions').

    Raises:
        HTTPException: Raises an appropriate HTTP error.
    """

    # Log the error with additional context
    api_logger.error(
        "Vector store action %s failed for organization %s in project %s",
        action,
        organization_id,
        project_id,
        exc_info=True,
    )

    if isinstance(e, HttpResponseError) and e.status_code == 429:
        api_logger.error(
            "Error creating vector store for project %s because the azure limit is reached",
            project_id,
            exc_info=True,
        )
        return JSONResponse(
            content={
                "error": "TooManyVectorStores",
                "message": e.message,
                "status_code": status.HTTP_507_INSUFFICIENT_STORAGE,
            },
            status_code=status.HTTP_507_INSUFFICIENT_STORAGE,
        )

    if isinstance(e, ResourceNotFoundError):
        api_logger.error(
            "Vector store or document for project %s and organization: %s not found.",
            project_id,
            organization_id,
            exc_info=True,
        )

        return JSONResponse(
            content={
                "error": "ResourceNotFoundError",
                "message": e.message,
                "status_code": status.HTTP_404_NOT_FOUND,
            },
            status_code=status.HTTP_404_NOT_FOUND,
        )

    if isinstance(e, HttpResponseError):
        api_logger.error(
            "An unexpected Http response error occurred: %s", e.message, exc_info=True
        )
        # Ensure e.status_code is a valid HTTP status code
        if e.status_code in vars(status).values():
            starlette_status = e.status_code
        else:
            # Handle invalid status codes or map to a default
            starlette_status = status.HTTP_500_INTERNAL_SERVER_ERROR
        return JSONResponse(
            content={
                "error": "HttpResponseError",
                "message": e.message,
                "status_code": e.status_code,
            },
            status_code=starlette_status,
        )

    # Raise a generic 500 error for unexpected failures, preserving the original exception
    return JSONResponse(
        content={
            "error": "InternalServerError",
            "message": f"Error generating {action} for organization {organization_id} in project {project_id}.",
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        },
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )
