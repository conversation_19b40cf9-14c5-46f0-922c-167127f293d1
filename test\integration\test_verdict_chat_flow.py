"""
End-to-end integration tests for the verdict chat flow.
Tests the complete flow: seed verdict → start chat → verify context.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from uuid import uuid4
import json

from src.app.main import app
from src.app.models.chat.chat_models_v1 import ChatRequest


class TestVerdictChatFlow:
    """End-to-end tests for verdict-based chat functionality."""

    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)

    @pytest.fixture
    def organization_id(self):
        """Sample organization ID."""
        return str(uuid4())

    @pytest.fixture
    def user_id(self):
        """Sample user ID."""
        return "test-user-123"

    @pytest.fixture
    def session_id(self):
        """Sample session ID."""
        return "test-session-456"

    @pytest.fixture
    def verdict_content(self):
        """Sample verdict content."""
        return """
ECLI:NL:HR:2023:123

Hoge Raad der Nederlanden
Datum: 15 januari 2023
Zaaknummer: 22/12345

UITSPRAAK

In de zaak van:
[<PERSON><PERSON><PERSON>], wonende te [Plaats],
appellant,
advocaat: mr. [<PERSON><PERSON>],

tegen

[Verweerder], wonende te [Plaats],
verweerder,
advocaat: mr. [Naam].

Het geding in cassatie

Appellant is in cassatie gekomen tegen een arrest van het gerechtshof Amsterdam van 15 maart 2022.

BESLISSING

De Hoge Raad:
- verwerpt het beroep in cassatie;
- veroordeelt appellant in de kosten van het geding in cassatie.

Deze uitspraak is gedaan door [Namen rechters] en in het openbaar uitgesproken op 15 januari 2023.
        """.strip()

    @pytest.fixture
    def seed_payload(self, session_id, verdict_content):
        """Sample seed message payload."""
        return {
            "conversation_id": session_id,
            "content": verdict_content,
            "role": "assistant",
            "order": 0,
            "verdict_id": "ECLI:NL:HR:2023:123",
            "metadata": {
                "court": "Hoge Raad",
                "date": "2023-01-15",
                "case_number": "22/12345",
                "case_type": "Civil"
            }
        }

    @pytest.fixture
    def chat_request_payload(self, session_id, user_id):
        """Sample chat request payload."""
        return {
            "session_id": session_id,
            "user_id": user_id,
            "user_message": "Can you explain the key points of this verdict?",
            "jurisprudence": True
        }

    def test_complete_verdict_chat_flow(self, client, organization_id, user_id, session_id, seed_payload, chat_request_payload, verdict_content):
        """Test the complete flow from seeding verdict to getting chat response."""
        
        # Step 1: Seed the verdict message
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "seed-message-id-123"
            
            seed_response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": user_id},
                json=seed_payload
            )
            
            assert seed_response.status_code == 201
            assert seed_response.json() == {"id": "seed-message-id-123"}

        # Step 2: Start chat conversation with the seeded verdict
        with patch('src.app.services.chat.chat_services_v1.ChatService.get_chat_response') as mock_chat_response:
            # Mock the chat response
            mock_response = Mock()
            mock_response.id = "chat-response-id-456"
            mock_response.organization_id = organization_id
            mock_response.project_id = None
            mock_response.session_id = session_id
            mock_response.user_id = user_id
            mock_response.user_message = chat_request_payload["user_message"]
            mock_response.content = f"Based on the verdict ECLI:NL:HR:2023:123, the key points are: 1) The Hoge Raad rejected the appeal, 2) The appellant was ordered to pay costs..."
            mock_response.prompt_tokens = 150
            mock_response.completion_tokens = 200
            mock_response.total_tokens = 350
            
            mock_chat_response.return_value = mock_response
            
            chat_response = client.post(
                f"/{organization_id}/chat",
                params={"use_streaming": False},
                json=chat_request_payload
            )
            
            assert chat_response.status_code == 200
            response_data = chat_response.json()
            
            # Verify the response contains verdict-related information
            assert "ECLI:NL:HR:2023:123" in response_data["content"]
            assert "Hoge Raad" in response_data["content"]
            assert response_data["session_id"] == session_id
            assert response_data["user_message"] == chat_request_payload["user_message"]

    def test_verdict_context_in_chat_history(self, client, organization_id, user_id, session_id, seed_payload, chat_request_payload):
        """Test that seeded verdict is available in chat history during conversation."""
        
        # Mock chat history with seeded verdict
        mock_verdict_message = Mock()
        mock_verdict_message.content = seed_payload["content"]
        mock_verdict_message.additional_kwargs = {'requires_response': False}
        
        mock_user_message = Mock()
        mock_user_message.content = chat_request_payload["user_message"]
        
        mock_history = [mock_verdict_message, mock_user_message]
        
        with patch('src.app.services.chat.chat_services_v1.ChatService') as mock_chat_service_class:
            mock_chat_service = Mock()
            mock_chat_service_class.return_value = mock_chat_service
            
            # Mock the chat response that should reference the verdict
            mock_response = Mock()
            mock_response.content = "Based on the Hoge Raad verdict you provided, I can explain that..."
            mock_response.id = "response-123"
            mock_response.organization_id = organization_id
            mock_response.session_id = session_id
            mock_response.user_id = user_id
            mock_response.user_message = chat_request_payload["user_message"]
            mock_response.prompt_tokens = 100
            mock_response.completion_tokens = 150
            mock_response.total_tokens = 250
            
            mock_chat_service.get_chat_response.return_value = mock_response
            
            response = client.post(
                f"/{organization_id}/chat",
                params={"use_streaming": False},
                json=chat_request_payload
            )
            
            assert response.status_code == 200
            response_data = response.json()
            assert "verdict" in response_data["content"].lower()

    def test_multiple_verdicts_in_conversation(self, client, organization_id, user_id, session_id):
        """Test handling multiple verdicts in the same conversation."""
        
        # First verdict
        verdict1_payload = {
            "conversation_id": session_id,
            "content": "ECLI:NL:HR:2023:123 - First verdict content",
            "role": "assistant",
            "verdict_id": "ECLI:NL:HR:2023:123"
        }
        
        # Second verdict (should be rejected as duplicate if same content)
        verdict2_payload = {
            "conversation_id": session_id,
            "content": "ECLI:NL:HR:2023:456 - Second verdict content",
            "role": "assistant",
            "verdict_id": "ECLI:NL:HR:2023:456"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            # First verdict succeeds
            mock_add_seed.return_value = "first-verdict-id"
            
            response1 = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": user_id},
                json=verdict1_payload
            )
            assert response1.status_code == 201
            
            # Second verdict also succeeds (different content)
            mock_add_seed.return_value = "second-verdict-id"
            
            response2 = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": user_id},
                json=verdict2_payload
            )
            assert response2.status_code == 201

    def test_chat_without_seeded_verdict(self, client, organization_id, user_id, session_id):
        """Test chat behavior when no verdict has been seeded."""
        
        chat_request = {
            "session_id": session_id,
            "user_id": user_id,
            "user_message": "Tell me about Dutch law",
            "jurisprudence": True
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatService.get_chat_response') as mock_chat_response:
            mock_response = Mock()
            mock_response.content = "I can help you with Dutch law questions. What specific area are you interested in?"
            mock_response.id = "response-no-verdict"
            mock_response.organization_id = organization_id
            mock_response.session_id = session_id
            mock_response.user_id = user_id
            mock_response.user_message = chat_request["user_message"]
            mock_response.prompt_tokens = 50
            mock_response.completion_tokens = 75
            mock_response.total_tokens = 125
            
            mock_chat_response.return_value = mock_response
            
            response = client.post(
                f"/{organization_id}/chat",
                params={"use_streaming": False},
                json=chat_request
            )
            
            assert response.status_code == 200
            # Should work normally without verdict context

    def test_streaming_chat_with_verdict(self, client, organization_id, user_id, session_id, chat_request_payload):
        """Test streaming chat response with seeded verdict."""
        
        with patch('src.app.services.chat.chat_services_v1.ChatService.get_streaming_chat_response') as mock_streaming:
            # Mock streaming response
            def mock_stream():
                yield {"content": "Based on the verdict ", "is_final": False}
                yield {"content": "ECLI:NL:HR:2023:123, ", "is_final": False}
                yield {"content": "the key points are...", "is_final": False}
                yield {"content": "", "is_final": True, "metadata": {"total_tokens": 100}}
            
            mock_streaming.return_value = mock_stream()
            
            response = client.post(
                f"/{organization_id}/chat",
                params={"use_streaming": True},
                json=chat_request_payload
            )
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/event-stream; charset=utf-8"

    def test_error_handling_in_verdict_flow(self, client, organization_id, user_id, session_id, seed_payload):
        """Test error handling throughout the verdict chat flow."""
        
        # Test seeding error
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.side_effect = Exception("Database error")
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": user_id},
                json=seed_payload
            )
            
            assert response.status_code == 500

    def test_verdict_metadata_preservation(self, client, organization_id, user_id, session_id):
        """Test that verdict metadata is preserved throughout the flow."""
        
        rich_metadata_payload = {
            "conversation_id": session_id,
            "content": "Verdict content with rich metadata",
            "role": "assistant",
            "verdict_id": "ECLI:NL:HR:2023:123",
            "metadata": {
                "court": "Hoge Raad",
                "date": "2023-01-15",
                "case_number": "22/12345",
                "legal_areas": ["Contract Law", "Civil Procedure"],
                "parties": {
                    "appellant": "Company A",
                    "respondent": "Company B"
                },
                "outcome": "Appeal rejected",
                "costs_awarded": True
            }
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "metadata-rich-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": user_id},
                json=rich_metadata_payload
            )
            
            assert response.status_code == 201
            
            # Verify the service received the full metadata
            call_args = mock_add_seed.call_args
            seed_message = call_args[0][0]
            assert seed_message.metadata["court"] == "Hoge Raad"
            assert seed_message.metadata["legal_areas"] == ["Contract Law", "Civil Procedure"]
            assert seed_message.metadata["parties"]["appellant"] == "Company A"
