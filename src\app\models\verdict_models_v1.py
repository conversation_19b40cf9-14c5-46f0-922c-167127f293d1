"""
DTOs and request / response models for the Rechtspraak search endpoint.
Only the verdict ID is returned- the caller looks up details in Cosmos.
"""

from typing import List, Optional

from pydantic import BaseModel, Field


class VerdictSearchQuery(BaseModel):
    """Validated query-string params coming from the router."""
    q: str = Field(..., min_length=3, description="Free-text search query")
    legal_areas: Optional[List[str]] = Field(
        default=None, description="Multi-select list of legal areas"
    )
    legal_sub_areas: Optional[List[str]] = Field(
        default=None, description="Multi-select list of legal sub-areas"
    )
    top: int = Field(
        default=10, ge=1, le=100, description="Maximum number of verdict IDs to return"
    )


class VerdictSearchResponse(BaseModel):
    """List of verdict IDs and a count (useful for paging / analytics)."""
    items: List[str]
    count: int