# Standard imports
import pytest
from pytest_mock import mocker

# External imports
from azure.core.exceptions import ResourceNotFoundError, HttpResponseError

# Internal imports
from src.app.services.retrieval.storage_services_v1 import BlobStorageService


class TestBlobStorageService:
    """
    Unit tests for the BlobStorageService class.
    """

    @pytest.fixture(name="storage_service")
    def blob_storage_service_fixture(self):
        """
        Creates an instance of BlobStorageService to be injected for mocks.
        """

        return BlobStorageService("fake_account", "fake_key")

    # TODO: Add tests for file name like: "Concept - akte verpanding aandelen AI Innovations B.V. d.d. 28-03-2025.pdf"
    @pytest.mark.asyncio
    async def test_delete_blob_when_blob_exists_should_delete_successfully(
        self, storage_service, mocker
    ):
        """
        Should successfully delete an existing blob.
        """

        # Arrange
        mock_blob_client = mocker.AsyncMock()
        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act
        await storage_service.delete_blob("container1", "blob1")

        # Assert
        mock_blob_client.delete_blob.assert_awaited_once()

    @pytest.mark.asyncio
    async def test_delete_blob_when_blob_not_found_should_raise_resource_not_found(
        self, storage_service, mocker
    ):
        """
        Should raise ResourceNotFoundError when blob doesn't exist.
        """

        # Arrange
        mock_blob_client = mocker.AsyncMock()
        mock_blob_client.delete_blob = mocker.AsyncMock(
            side_effect=ResourceNotFoundError("Blob not found")
        )

        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act & Assert
        with pytest.raises(ResourceNotFoundError, match="Blob blob1 not found"):
            await storage_service.delete_blob("container1", "blob1")

    @pytest.mark.asyncio
    async def test_download_blob_when_blob_exists_should_return_content(
        self, storage_service, mocker
    ):
        """
        Should return the content of an existing blob.
        """

        # Arrange
        mock_blob_stream = mocker.AsyncMock()
        mock_blob_stream.readall = mocker.AsyncMock(return_value=b"test content")

        mock_blob_client = mocker.AsyncMock()
        mock_blob_client.download_blob = mocker.AsyncMock(return_value=mock_blob_stream)

        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act
        content = await storage_service.download_blob("container1", "blob1")

        # Assert
        assert content == b"test content"
        mock_blob_stream.readall.assert_awaited_once()

    @pytest.mark.asyncio
    async def test_download_blob_when_blob_not_found_should_raise_resource_not_found(
        self, storage_service, mocker
    ):
        """
        Should raise ResourceNotFoundError when blob doesn't exist.
        """

        # Arrange
        mock_blob_client = mocker.AsyncMock()
        mock_blob_client.download_blob = mocker.AsyncMock(
            side_effect=ResourceNotFoundError("Blob not found")
        )

        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act & Assert
        with pytest.raises(ResourceNotFoundError, match="Blob blob1 not found"):
            await storage_service.download_blob("container1", "blob1")

    @pytest.mark.asyncio
    async def test_get_blob_client_when_empty_args_should_raise_value_error(
        self,
        storage_service,
    ):
        """
        Should raise ValueError when container or blob names are empty.
        """

        # Act & Assert
        with pytest.raises(
            ValueError, match="Container name and blob name must be provided"
        ):
            storage_service._get_blob_client(
                "", "blob1"
            )  # TODO: Fix this (protected access)

        with pytest.raises(
            ValueError, match="Container name and blob name must be provided"
        ):
            storage_service._get_blob_client("container1", "")

    @pytest.mark.asyncio
    async def test_get_blob_metadata_when_blob_exists_should_return_metadata(
        self, storage_service, mocker
    ):
        """
        Should return metadata successfully when blob exists.
        """

        # Arrange
        mock_blob_metadata = {
            "metadata": {"key1": "value1", "key2": "value2"},
        }

        mocker.patch.object(
            storage_service,
            "get_blob_properties",
            new_callable=mocker.AsyncMock,
            return_value=mock_blob_metadata,
        )

        # Act
        metadata = await storage_service.get_blob_metadata("container1", "blob1")

        # Assert
        storage_service.get_blob_properties.assert_awaited_once_with(
            "container1", "blob1"
        )
        assert metadata == mock_blob_metadata["metadata"]

    @pytest.mark.asyncio
    async def test_get_blob_metadata_when_blob_not_found_should_raise_resource_not_found(
        self, storage_service, mocker
    ):
        """
        Should raise ResourceNotFoundError when blob doesn't exist.
        """

        # Arrange
        mocker.patch.object(
            storage_service,
            "get_blob_properties",
            side_effect=ResourceNotFoundError("Blob not found"),
        )

        # Act & Assert
        with pytest.raises(ResourceNotFoundError):
            await storage_service.get_blob_metadata("container1", "blob1")

    @pytest.mark.asyncio
    async def test_get_blob_properties_when_blob_exists_should_return_properties(
        self, storage_service, mocker
    ):
        """
        Should return blob properties when blob exists.
        """

        # Arrange
        mock_blob_client = mocker.AsyncMock()
        mock_blob_client.get_blob_properties = mocker.AsyncMock(
            return_value=mocker.Mock(
                metadata={"key": "value"},
                size=1024,
                last_modified="2023-01-01",
                content_settings=mocker.Mock(content_type="application/pdf"),
                blob_type="BlockBlob",
                etag="0x8DABC12345",
            )
        )

        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act
        properties = await storage_service.get_blob_properties("container1", "blob1")

        # Assert
        assert properties["metadata"] == {"key": "value"}
        assert properties["size"] == 1024
        assert properties["content_type"] == "application/pdf"

    @pytest.mark.asyncio
    async def test_get_blob_properties_when_blob_not_found_should_raise_error(
        self, storage_service, mocker
    ):
        """
        Should raise ResourceNotFoundError when blob doesn't exist.
        """

        # Arrange
        mock_blob_client = mocker.AsyncMock()
        mock_blob_client.get_blob_properties = mocker.AsyncMock(
            side_effect=ResourceNotFoundError("Blob not found")
        )

        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act & Assert
        with pytest.raises(ResourceNotFoundError, match="Blob blob1 not found"):
            await storage_service.get_blob_properties("container1", "blob1")

    @pytest.mark.asyncio
    async def test_list_blobs_when_container_not_found_should_raise_error(
        self, storage_service, mocker
    ):
        """
        Should raise ResourceNotFoundError when container doesn't exist.
        """

        # Arrange
        async def async_gen():
            raise ResourceNotFoundError("Container not found")
            yield

        mock_container_client = mocker.AsyncMock()
        mock_container_client.list_blobs = mocker.Mock(return_value=async_gen())

        mocker.patch.object(
            storage_service._service_client,  # TODO: Fix this later
            "get_container_client",
            return_value=mock_container_client,
        )

        # Act & Assert
        with pytest.raises(
            ResourceNotFoundError, match="Container container1 not found"
        ):
            async for _ in storage_service.list_blobs("container1"):
                pass

    @pytest.mark.asyncio
    async def test_list_blobs_with_include_metadata_should_include_metadata(
        self, storage_service, mocker
    ):
        """
        Should include metadata when requested.
        """

        # Arrange
        async def async_gen():
            return
            yield

        mock_container_client = mocker.AsyncMock()
        mock_container_client.list_blobs = mocker.Mock(return_value=async_gen())

        mocker.patch.object(
            storage_service._service_client,
            "get_container_client",
            return_value=mock_container_client,
        )

        # Act
        async for _ in storage_service.list_blobs(  # TODO: This test should be improved
            "container1", include_datasets="metadata"
        ):
            pass

        # Assert
        mock_container_client.list_blobs.assert_called_once_with(
            name_starts_with="", include="metadata"
        )

    @pytest.mark.asyncio
    async def test_list_blobs_with_prefix_should_return_matching_blobs(
        self, storage_service, mocker
    ):
        """
        Should return blobs matching the prefix.
        """

        # Arrange
        mock_blob1 = mocker.Mock(name="blob1")
        mock_blob2 = mocker.Mock(name="blob2")

        async def async_gen():
            yield mock_blob1
            yield mock_blob2

        mock_container_client = mocker.AsyncMock()
        mock_container_client.list_blobs = mocker.Mock(return_value=async_gen())

        mocker.patch.object(
            storage_service._service_client,  # TODO: Fix this access to a protected member
            "get_container_client",
            return_value=mock_container_client,
        )

        # Act
        blobs = []
        async for blob in storage_service.list_blobs("container1", prefix="prefix"):
            blobs.append(blob)

        # Assert
        assert len(blobs) == 2
        mock_container_client.list_blobs.assert_called_once_with(
            name_starts_with="prefix", include=None
        )

    @pytest.mark.asyncio
    async def test_upload_blob_when_error_occurs_should_raise_exception(
        self, storage_service, mocker
    ):
        """
        Should raise HttpResponseError when upload fails.
        """

        # Arrange
        mock_blob_client = mocker.AsyncMock()
        mock_blob_client.upload_blob = mocker.AsyncMock(
            side_effect=HttpResponseError("Upload failed")
        )
        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act & Assert
        with pytest.raises(HttpResponseError, match="Failed to upload blob blob1"):
            await storage_service.upload_blob("container1", "blob1", b"content")

    @pytest.mark.asyncio
    async def test_upload_blob_with_metadata_should_set_metadata_correctly(
        self, storage_service, mocker
    ):
        """
        Should upload blob with correct metadata.
        """

        # Arrange
        mock_blob_client = mocker.AsyncMock()
        mock_blob_client.upload_blob = mocker.AsyncMock()
        mocker.patch.object(
            storage_service, "_get_blob_client", return_value=mock_blob_client
        )

        # Act
        await storage_service.upload_blob(
            "container1",
            "blob1",
            b"content",
            metadata={"key": "value"},
            overwrite=False,
        )

        # Assert
        mock_blob_client.upload_blob.assert_awaited_once_with(
            b"content", metadata={"key": "value"}, overwrite=False
        )
