from uuid import UUID
from unittest.mock import Magic<PERSON>ock
import os
import pytest
import pytest_asyncio
from azure.core.exceptions import ResourceNotFoundError, HttpResponseError
from langchain_openai import AzureOpenAIEmbeddings
from langchain_community.vectorstores.azuresearch import AzureSearch
from pytest_mock import <PERSON><PERSON><PERSON>ixture

from src.app.services.retrieval.retrieval_services_v1 import RetrieverService
from unittest.mock import AsyncMock, Mock


class TestRetrieverService:
    @pytest.fixture
    def organization_id(self):
        return UUID("550e8400-e29b-41d4-a716-************")

    @pytest.fixture
    def project_id(self):
        return UUID("550e8400-e29b-41d4-a716-************")

    @pytest.fixture
    def doc_id(self):
        return UUID("550e8400-e29b-41d4-a716-************")

    @pytest.fixture(autouse=True)
    def setup_environment(self):
        """Setup environment variables needed for testing"""
        os.environ["AZURE_OPENAI_API_KEY"] = "test-key"
        os.environ["AZURE_OPENAI_API_VERSION"] = "2023-05-15"
        os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"] = "test-deployment"
        os.environ["AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT_NAME"] = "test-embeddings"
        os.environ["AZURE_OPENAI_ENDPOINT"] = "https://test.openai.azure.com"
        os.environ["AZURE_STORAGE_ACCOUNT_CONTAINER_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_DATASOURCE_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_INDEX_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_INDEXER_PREFIX"] = "test-"
        os.environ["AZURE_STORAGE_ACCOUNT_PATH_PREFIX"] = "test-"
        os.environ["AZURE_SEARCH_ADMIN_KEY"] = "test-search-key"
        os.environ["AZURE_SEARCH_ENDPOINT"] = "https://test.search.windows.net"
        os.environ["AZURE_SEARCH_TOP_K_RESULTS"] = "50"
        os.environ["AZURE_STORAGE_ACCOUNT_KEY"] = "test-storage-key"
        os.environ["AZURE_STORAGE_ACCOUNT_NAME"] = "teststorage"
        yield
        # Cleanup
        del os.environ["AZURE_OPENAI_API_KEY"]
        del os.environ["AZURE_OPENAI_API_VERSION"]
        del os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"]
        del os.environ["AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT_NAME"]
        del os.environ["AZURE_OPENAI_ENDPOINT"]
        del os.environ["AZURE_STORAGE_ACCOUNT_CONTAINER_PREFIX"]
        del os.environ["AZURE_SEARCH_DATASOURCE_PREFIX"]
        del os.environ["AZURE_SEARCH_INDEX_PREFIX"]
        del os.environ["AZURE_SEARCH_INDEXER_PREFIX"]
        del os.environ["AZURE_STORAGE_ACCOUNT_PATH_PREFIX"]
        del os.environ["AZURE_SEARCH_ADMIN_KEY"]
        del os.environ["AZURE_SEARCH_ENDPOINT"]
        del os.environ["AZURE_SEARCH_TOP_K_RESULTS"]
        del os.environ["AZURE_STORAGE_ACCOUNT_KEY"]
        del os.environ["AZURE_STORAGE_ACCOUNT_NAME"]

    @pytest.fixture
    def retriever_service(self, mocker: MockerFixture, organization_id):
        # Mock Azure clients
        mock_embeddings = mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.AzureOpenAIEmbeddings"
        )
        mock_embeddings.return_value.embed_query.return_value = [0.1] * 1536
        mock_azure_search = mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.AzureSearch"
        )

        from src.app.services.retrieval.retrieval_services_v1 import RetrieverService

        return RetrieverService(organization_id=organization_id, include_jurisprudence=True)

    def test_retrieve_projects(self, retriever_service, organization_id, mocker: MockerFixture):
        """Test retrieving projects"""
        # Mock Azure clients
        mock_embeddings = mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.AzureOpenAIEmbeddings"
        )
        mock_embeddings.return_value.embed_query.return_value = [0.1] * 1536

        # Create a mock for the search method
        expected_results = [MagicMock()]
        retriever_service._project_search_client = Mock()
        retriever_service._project_search_client.search.return_value = expected_results

        # Call the function
        result = retriever_service.retrieve_project("query")

        # Assert the result
        assert len(result) == 1
        assert isinstance(result[0], MagicMock)

    def test_search_jurisprudence_nonexistent_raises_error(self, organization_id, mocker):
        """Test searching in non-existent jurisprudence index"""
        # Mock AzureOpenAIEmbeddings
        mock_embeddings = mocker.Mock(spec=AzureOpenAIEmbeddings)
        mock_embeddings.embed_query.return_value = [0.0] * 1536
        mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.AzureOpenAIEmbeddings",
            return_value=mock_embeddings,
        )

        # Mock AzureSearch client
        mock_search_client = mocker.Mock()
        mock_search_client.client.search.side_effect = ResourceNotFoundError(
            "Jurisprudence index not found"
        )
        mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.AzureSearch",
            return_value=mock_search_client,
        )

        from src.app.services.retrieval.retrieval_services_v1 import RetrieverService

        service = RetrieverService(organization_id=organization_id, include_jurisprudence=True)

        with pytest.raises(ResourceNotFoundError):
            service.search_jurisprudence("test query")

    @pytest.mark.asyncio
    async def test_retrieve_jurisprudence(self, retriever_service, mocker):
        """Test retrieving jurisprudence"""
        # Mock Azure clients
        mock_embeddings = mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.AzureOpenAIEmbeddings"
        )
        mock_embeddings.return_value.embed_query.return_value = [0.1] * 1536

        # Create a mock for the search method
        expected_results = [{"text": "Jurisprudence result"}]
        retriever_service._jurisprudence_search_client = Mock()
        retriever_service._jurisprudence_search_client.search.return_value = expected_results

        # Call the function
        result = await retriever_service.retrieve_jurisprudence("query")
        assert result == expected_results

    @pytest.mark.asyncio
    async def test_retrieve_jurisprudence_not_found(self, retriever_service, mocker):
        """Test retrieving jurisprudence when no results are found"""
        # Configure mock to raise ResourceNotFoundError
        retriever_service._jurisprudence_search_client = Mock()
        retriever_service._jurisprudence_search_client.search.side_effect = (
            ResourceNotFoundError()
        )

        # Call the function
        with pytest.raises(ResourceNotFoundError):
            await retriever_service.retrieve_jurisprudence("query")

    def test_retrieve_projects_with_filters(self, retriever_service, mocker):
        """Test retrieving projects with filters"""
        # Mock Azure clients
        mock_embeddings = mocker.patch(
            "src.app.services.retrieval.retrieval_services_v1.AzureOpenAIEmbeddings"
        )
        mock_embeddings.return_value.embed_query.return_value = [0.1] * 1536

        # Create a mock for the search method
        expected_results = [{"text": "Project result"}]
        retriever_service._project_search_client = Mock()
        retriever_service._project_search_client.search.return_value = expected_results

        # Set filter parameters
        retriever_service.document_type = "project_type"

        # Call the function
        result = retriever_service.retrieve_project("query")
        assert result == expected_results

    def test_build_project_filters(self, retriever_service):
        """Test building project filters with various parameters"""
        retriever_service.project_id = UUID("550e8400-e29b-41d4-a716-************")
        retriever_service.document_type = "test_type"
        retriever_service.document_ids = [UUID("550e8400-e29b-41d4-a716-************")]

        filters = retriever_service.build_project_filters()

        # Assert the filter string is correctly formatted
        assert f"project_id eq '{retriever_service.project_id}'" in filters
        assert f"document_type eq '{retriever_service.document_type}'" in filters

    def test_build_jurisprudence_filters(self, retriever_service):
        """Test building jurisprudence filters with various parameters"""
        retriever_service.legal_areas = ["area1", "area2"]
        retriever_service.legal_sub_areas = ["sub1", "sub2"]

        filters = retriever_service.build_jurisprudence_filters()

        # Assert the filter string is correctly formatted
        assert "search.in(legal_area, 'area1|area2', '|')" in filters
        assert "search.in(legal_sub_area, 'sub1|sub2', '|')" in filters
