"""
Performance and edge case tests for verdict chat functionality.
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock
from uuid import uuid4
import json

from src.app.main import app
from src.app.models.chat.chat_models_v1 import SeedMessageIn


class TestVerdictChatPerformance:
    """Performance tests for verdict chat functionality."""

    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)

    @pytest.fixture
    def organization_id(self):
        """Sample organization ID."""
        return str(uuid4())

    def test_large_verdict_content_performance(self, client, organization_id):
        """Test performance with very large verdict content."""
        # Create a large verdict (1MB of text)
        large_content = "A" * (1024 * 1024)  # 1MB
        
        large_payload = {
            "conversation_id": "perf-test-123",
            "content": large_content,
            "role": "assistant",
            "verdict_id": "ECLI:NL:HR:2023:LARGE"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "large-message-id"
            
            start_time = time.time()
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "perf-user"},
                json=large_payload
            )
            end_time = time.time()
            
            assert response.status_code == 201
            # Should complete within reasonable time (adjust threshold as needed)
            assert (end_time - start_time) < 5.0  # 5 seconds max

    def test_concurrent_seed_requests(self, client, organization_id):
        """Test concurrent seed message requests."""
        
        def make_seed_request(session_id):
            payload = {
                "conversation_id": f"concurrent-{session_id}",
                "content": f"Concurrent verdict content {session_id}",
                "role": "assistant",
                "verdict_id": f"ECLI:NL:HR:2023:{session_id}"
            }
            
            with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
                mock_add_seed.return_value = f"concurrent-id-{session_id}"
                
                response = client.post(
                    f"/{organization_id}/chat/messages/seed",
                    params={"user_id": f"user-{session_id}"},
                    json=payload
                )
                return response.status_code
        
        # Test with 10 concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_seed_request, i) for i in range(10)]
            results = [future.result() for future in futures]
        
        # All requests should succeed
        assert all(status == 201 for status in results)

    def test_memory_usage_with_multiple_verdicts(self, client, organization_id):
        """Test memory usage when seeding multiple verdicts in sequence."""
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "memory-test-id"
            
            # Seed 100 verdicts in sequence
            for i in range(100):
                payload = {
                    "conversation_id": f"memory-test-{i}",
                    "content": f"Verdict content {i} " * 1000,  # ~15KB each
                    "role": "assistant",
                    "verdict_id": f"ECLI:NL:HR:2023:{i:03d}"
                }
                
                response = client.post(
                    f"/{organization_id}/chat/messages/seed",
                    params={"user_id": "memory-user"},
                    json=payload
                )
                
                assert response.status_code == 201

    def test_unicode_and_special_characters_performance(self, client, organization_id):
        """Test performance with unicode and special characters."""
        
        # Create content with various unicode characters
        unicode_content = """
        🏛️ Rechtbank Amsterdam 📅 2023-01-15
        
        Uitspraak in de zaak tussen:
        • Eiser: Müller & Søn B.V. (€1.000.000 claim)
        • Verweerder: Société Française S.A.
        
        中文测试 العربية тест русский
        
        Special chars: @#$%^&*()_+-=[]{}|;':\",./<>?
        
        Mathematical symbols: ∑∏∫∆∇∂∞≠≤≥±×÷
        
        Emoji test: 👨‍⚖️👩‍💼📋📊💼🏢
        """ * 100  # Repeat to make it substantial
        
        payload = {
            "conversation_id": "unicode-test-123",
            "content": unicode_content,
            "role": "assistant",
            "verdict_id": "ECLI:NL:HR:2023:UNICODE"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "unicode-message-id"
            
            start_time = time.time()
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "unicode-user"},
                json=payload
            )
            end_time = time.time()
            
            assert response.status_code == 201
            assert (end_time - start_time) < 3.0  # Should handle unicode efficiently


class TestVerdictChatEdgeCases:
    """Edge case tests for verdict chat functionality."""

    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)

    @pytest.fixture
    def organization_id(self):
        """Sample organization ID."""
        return str(uuid4())

    def test_empty_string_content(self, client, organization_id):
        """Test handling of empty string content."""
        payload = {
            "conversation_id": "empty-test-123",
            "content": "",
            "role": "assistant"
        }
        
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            params={"user_id": "empty-user"},
            json=payload
        )
        
        # Should be rejected at validation level
        assert response.status_code == 422

    def test_whitespace_only_content(self, client, organization_id):
        """Test handling of whitespace-only content."""
        payload = {
            "conversation_id": "whitespace-test-123",
            "content": "   \n\t\r   ",
            "role": "assistant"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.side_effect = ValueError("Seed message content cannot be empty.")
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "whitespace-user"},
                json=payload
            )
            
            assert response.status_code == 500  # Service-level validation error

    def test_null_values_in_payload(self, client, organization_id):
        """Test handling of null values in payload."""
        payload = {
            "conversation_id": "null-test-123",
            "content": "Valid content",
            "role": "assistant",
            "verdict_id": None,  # Explicitly null
            "metadata": None     # Explicitly null
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "null-test-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "null-user"},
                json=payload
            )
            
            assert response.status_code == 201

    def test_extremely_long_conversation_id(self, client, organization_id):
        """Test handling of extremely long conversation ID."""
        long_conversation_id = "a" * 10000  # 10KB conversation ID
        
        payload = {
            "conversation_id": long_conversation_id,
            "content": "Test content",
            "role": "assistant"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "long-id-test"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "long-id-user"},
                json=payload
            )
            
            # Should handle long IDs gracefully
            assert response.status_code in [201, 422]  # Either success or validation error

    def test_malformed_json_payload(self, client, organization_id):
        """Test handling of malformed JSON."""
        malformed_json = '{"conversation_id": "test", "content": "test", "role": "assistant"'  # Missing closing brace
        
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            params={"user_id": "malformed-user"},
            data=malformed_json,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # JSON parsing error

    def test_invalid_uuid_organization_id(self, client):
        """Test handling of invalid UUID for organization ID."""
        payload = {
            "conversation_id": "uuid-test-123",
            "content": "Test content",
            "role": "assistant"
        }
        
        response = client.post(
            "/not-a-valid-uuid/chat/messages/seed",
            params={"user_id": "uuid-user"},
            json=payload
        )
        
        assert response.status_code == 422  # UUID validation error

    def test_missing_required_parameters(self, client, organization_id):
        """Test various combinations of missing required parameters."""
        
        # Missing user_id parameter
        payload = {
            "conversation_id": "missing-param-test",
            "content": "Test content",
            "role": "assistant"
        }
        
        response = client.post(
            f"/{organization_id}/chat/messages/seed",
            json=payload
        )
        
        assert response.status_code == 422

    def test_extremely_nested_metadata(self, client, organization_id):
        """Test handling of deeply nested metadata."""
        
        # Create deeply nested metadata
        nested_metadata = {"level1": {"level2": {"level3": {"level4": {"level5": "deep_value"}}}}}
        for i in range(10):  # Make it even deeper
            nested_metadata = {"deeper": nested_metadata}
        
        payload = {
            "conversation_id": "nested-test-123",
            "content": "Test content with nested metadata",
            "role": "assistant",
            "metadata": nested_metadata
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "nested-test-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "nested-user"},
                json=payload
            )
            
            # Should handle nested structures
            assert response.status_code == 201

    def test_binary_content_in_json(self, client, organization_id):
        """Test handling of binary-like content in JSON."""
        
        # Content that might cause encoding issues
        binary_like_content = "".join(chr(i) for i in range(32, 127))  # ASCII printable chars
        
        payload = {
            "conversation_id": "binary-test-123",
            "content": binary_like_content,
            "role": "assistant"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "binary-test-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "binary-user"},
                json=payload
            )
            
            assert response.status_code == 201

    def test_sql_injection_attempt_in_content(self, client, organization_id):
        """Test handling of SQL injection attempts in content."""
        
        sql_injection_content = """
        '; DROP TABLE verdicts; --
        UNION SELECT * FROM users WHERE '1'='1
        <script>alert('xss')</script>
        """
        
        payload = {
            "conversation_id": "sql-test-123",
            "content": sql_injection_content,
            "role": "assistant"
        }
        
        with patch('src.app.services.chat.chat_services_v1.ChatHistoryService.add_seed_message') as mock_add_seed:
            mock_add_seed.return_value = "sql-test-id"
            
            response = client.post(
                f"/{organization_id}/chat/messages/seed",
                params={"user_id": "sql-user"},
                json=payload
            )
            
            # Should treat as regular content, not execute
            assert response.status_code == 201
