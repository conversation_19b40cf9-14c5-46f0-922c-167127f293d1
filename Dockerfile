# syntax=docker/dockerfile:1

# Using Python 3.12 for now
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies for OpenCV and RapidOCR
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r /app/requirements.txt

COPY ./src /app/src

EXPOSE 9092

# Set environment variables
ENV APP_MODULE=src.app.main:fastapi_app
ENV PORT=9092
ENV WORKERS=3
ENV TIMEOUT=120

# Run the app using Gunicorn with Uvicorn workers
CMD ["sh", "-c", "gunicorn -w $WORKERS -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT --timeout $TIMEOUT $APP_MODULE"]