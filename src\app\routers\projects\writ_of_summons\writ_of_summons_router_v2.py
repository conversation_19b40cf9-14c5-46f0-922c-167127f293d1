"""
This module defines FastAPI routers for writ of summons operations.
"""

# Standard imports
import json
from typing import Optional, AsyncGenerator
from uuid import UUID

# External imports
from fastapi import APIRouter, Header, HTTPException, Path, Query, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import get_current_span, SpanKind, Status, StatusCode
from starlette.responses import StreamingResponse, JSONResponse

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v2 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.models.documents.document_models_v2 import DocumentType
from src.app.models.projects.completion_models_v2 import (
    CompletionOptions,
    CompletionType,
    ProjectCompletionResponse,
)

from src.app.services.completion.completion_services_v2 import ProjectCompletionService
from src.app.services.retrieval.retrieval_services_v2 import RetrieverService


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


async def stream_generator(
    completion_service: ProjectCompletionService,
    input_text: str,
    completion_options: CompletionOptions,
    top: Optional[int] = None,
) -> AsyncGenerator[str, None]:
    """
    Generates a stream of chat responses.
    Each chunk is formatted as JSON for easy parsing by the C# client.
    Args:
        completion_service (ProjectCompletionService): The completion service to use.
        input_text (str): The input text to generate completions from.
        completion_options (CompletionOptions): The completion options to use.
        top (Optional[int]): The number of completions to generate.
    Yields:
        StreamingChunk: A chunk of the AI response as it is generated
    Raises:
        HTTPException: If an error occurs during the streaming process.
    """

    try:
        async for chunk in completion_service.get_completion_stream(
            input_text, completion_options=completion_options
        ):
            # Convert chunk to JSON that C# can easily parse
            chunk_dict = chunk.model_dump()
            yield f"{json.dumps(chunk_dict)}\n"

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        ) from e


async def generate_response(
    organization_id: UUID,
    project_id: UUID,
    completion_options: CompletionOptions,
    document_type: Optional[DocumentType] = None,
    input_text: str = "*",
    use_streaming: bool = False,
) -> StreamingResponse | JSONResponse:
    """
    Generates a response based on the specified prompt type for a given project.

    This function retrieves the necessary documents for the project, generates a completion
    using the specified prompt type, and returns the result as a JSON response.
    """

    try:
        retriever_service = RetrieverService(
            organization_id=organization_id,
            project_id=project_id if project_id else None,
            document_type=document_type.name if document_type else None,
        )

        completion_service = ProjectCompletionService(retriever_service)
        if use_streaming:
            return StreamingResponse(
                stream_generator(
                    completion_service,
                    input_text=input_text,
                    completion_options=completion_options,
                ),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream",
                    "X-Accel-Buffering": "no",
                },
            )
        completion_results = await completion_service.get_completion(
            input_text=input_text,
            completion_options=completion_options,
        )

        # The field will be in the response model with the generated content
        content_field = get_content_field(completion_options.completion_type)
        response_data = ProjectCompletionResponse(
            id=completion_results.id,
            project_id=project_id,
            organization_id=organization_id,
            **{content_field: completion_results.content},
            prompt_tokens=completion_results.response_metadata["token_usage"][
                "prompt_tokens"
            ],
            completion_tokens=completion_results.response_metadata["token_usage"][
                "completion_tokens"
            ],
            total_tokens=completion_results.response_metadata["token_usage"][
                "total_tokens"
            ],
        )

        return JSONResponse(
            content=jsonable_encoder(response_data),
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        api_logger.error(
            "Error generating response for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        current_span = get_current_span()
        if current_span:
            current_span.set_status(Status(StatusCode.ERROR, str(e)))

        # Raise so the caller (route) can handle it
        raise e


def get_content_field(completion_type: CompletionType) -> str:
    """
    Maps a completion type to the corresponding field in the project response model.

    Args:
        completion_type (CompletionType): The type of completion to be mapped.

    Returns:
        str: The name of the field in the response model where the generated content
        will be placed.
    """

    content_field_mapping = {
        CompletionType.WRIT_OF_SUMMONS_CONTRADICTIONS: "writ_of_summons_contradictions",
        CompletionType.WRIT_OF_SUMMONS_QUESTIONS: "writ_of_summons_questions",
        CompletionType.WRIT_OF_SUMMONS_SUMMARY: "writ_of_summons_summary",
        CompletionType.WRIT_OF_SUMMONS_TIMELINE: "writ_of_summons_timeline",
    }

    return content_field_mapping[completion_type]


@api_router.get(
    "/{organization_id}/projects/{project_id}/writ_of_summons/contradictions",
    description="Generates contradictions based on all writ of summons documents in a project.",
    operation_id="generate_writ_of_summons_contradictions",
    response_description="The generated contradictions for the writ of summons.",
    response_model=ProjectCompletionResponse,
    summary="Contradictions for writ of summons. ☑️",
)
async def generate_writ_of_summons_contradictions(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates contradictions based on all writ of summons documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated contradictions.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.WRIT_OF_SUMMONS_CONTRADICTIONS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                DocumentType.WRIT_OF_SUMMONS,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating writ of summons contradictions for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating writ of summons contradictions for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/writ_of_summons/questions",
    description="Generates questions based on the writ of summons in a specific project.",
    operation_id="generate_writ_of_summons_questions",
    response_description="The generated questions for the writ of summons.",
    response_model=ProjectCompletionResponse,
    summary="Questions for writ of summons. ☑️",
)
async def generate_writ_of_summons_questions(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates questions based on the writ of summons in a specific project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated questions for the writ of summons.
    """

    try:
        completion_options = CompletionOptions(
            CompletionType.WRIT_OF_SUMMONS_QUESTIONS,
            params={
                "claimant": claimant,
                "defendant": defendant,
            },
        )

        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                DocumentType.WRIT_OF_SUMMONS,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating writ of summons questions for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating writ of summons questions for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/writ_of_summons/summary",
    description="Generates a summary for the writ of summons in a specific project.",
    operation_id="generate_writ_of_summons_summary",
    response_description="The generated summary for the writ of summons.",
    response_model=ProjectCompletionResponse,
    summary="Summary for writ of summons. ☑️",
)
async def generate_writ_of_summons_summary(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates a summary for the writ of summons in a specific project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated summary.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.WRIT_OF_SUMMONS_SUMMARY,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )
            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                DocumentType.WRIT_OF_SUMMONS,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating writ of summons summary for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating writ of summons summary for organization {organization_id} in project {project_id}.",
        ) from e


@api_router.get(
    "/{organization_id}/projects/{project_id}/writ_of_summons/timeline",
    description="Generates a timeline of important events based on the writ of summons documents in a project.",
    operation_id="generate_writ_of_summons_timeline",
    response_description="The generated timeline of important events for the writ of summons.",
    response_model=ProjectCompletionResponse,
    summary="Timeline for writ of summons. ☑️",
)
async def generate_writ_of_summons_timeline(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates a timeline of important events based on the writ of summons documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated timeline of important events.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.WRIT_OF_SUMMONS_TIMELINE,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                DocumentType.WRIT_OF_SUMMONS,
                use_streaming=use_streaming,
            )

    except Exception as e:
        api_logger.error(
            "Error generating writ of summons timeline for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating writ of summons timeline for organization {organization_id} in project {project_id}.",
        ) from e
